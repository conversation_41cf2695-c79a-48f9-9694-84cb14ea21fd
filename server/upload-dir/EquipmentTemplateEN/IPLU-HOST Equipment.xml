<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="Equipment Template List">
  <EquipmentTemplate EquipmentTemplateId="755000004" ParentTemplateId="0" EquipmentTemplateName="IPLU-HOST Equipment" ProtocolCode="IPLU-HOST Equipment 6-00" EquipmentCategory="99" EquipmentType="2" Memo="2012-4-17 15:01:28:First time import the template." Property="1/3" Decription=" " EquipmentStyle=" " Unit=" " Vender=" ">
    <Signals Name="Template Signal">
      <Signal SignalId="755000396" SignalName="Ethernet Port Work Mode" SignalCategory="2" SignalType="1" ChannelNo="10" ChannelType="2" Expression=" " DataType="" ShowPrecision=" " Unit=" " StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="1" SignalProperty="27" SignalMeanings="0:Half-duplex;1:Full-duplex" />
      <Signal SignalId="755000397" SignalName="Ethernet Port Connection Speed" SignalCategory="2" SignalType="1" ChannelNo="9" ChannelType="2" Expression=" " DataType="" ShowPrecision=" " Unit=" " StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="2" SignalProperty="27" SignalMeanings="0:10M;1:100M" />
      <Signal SignalId="755000398" SignalName="Software Version Number" SignalCategory="1" SignalType="1" ChannelNo="0" ChannelType="1" Expression=" " DataType="" ShowPrecision="0.00" Unit="Ver" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="3" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000399" SignalName="Hardware PCB Version Number" SignalCategory="1" SignalType="1" ChannelNo="1" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="Ver" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="4" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000400" SignalName="Hardware Programmable Device Version Number" SignalCategory="1" SignalType="1" ChannelNo="2" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="Ver" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="5" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000401" SignalName="Compile Time Year" SignalCategory="1" SignalType="1" ChannelNo="3" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="Year" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="6" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000402" SignalName="Compile Time Month" SignalCategory="1" SignalType="1" ChannelNo="4" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="Month" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="7" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000403" SignalName="Compile Time Day" SignalCategory="1" SignalType="1" ChannelNo="5" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="Day" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="8" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000404" SignalName="Ethernet Port Connection State" SignalCategory="2" SignalType="1" ChannelNo="8" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="9" SignalProperty="27" SignalMeanings="0:Disconnected;1:Connected" />
      <Signal SignalId="755000405" SignalName="LOS-A State" SignalCategory="2" SignalType="1" ChannelNo="11" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="10" SignalProperty="27" SignalMeanings="0:Normal;1:Disconnected Alarm" />
      <Signal SignalId="755000406" SignalName="LOF-A State" SignalCategory="2" SignalType="1" ChannelNo="12" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="11" SignalProperty="27" SignalMeanings="0:Normal;1:Step Out Alarm" />
      <Signal SignalId="755000407" SignalName="LOS-B State" SignalCategory="2" SignalType="1" ChannelNo="13" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="12" SignalProperty="27" SignalMeanings="0:Normal;1:Disconnected Alarm" />
      <Signal SignalId="755000408" SignalName="LOF-B State" SignalCategory="2" SignalType="1" ChannelNo="14" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="13" SignalProperty="27" SignalMeanings="0:Normal;1:Step Out Alarm" />
      <Signal SignalId="755000409" SignalName="SDRAM State" SignalCategory="2" SignalType="1" ChannelNo="40" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="14" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000410" SignalName="Flash State" SignalCategory="2" SignalType="1" ChannelNo="41" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="15" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000411" SignalName="2.5V Reference State" SignalCategory="2" SignalType="1" ChannelNo="42" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="16" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000412" SignalName="0V Reference State" SignalCategory="2" SignalType="1" ChannelNo="43" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="17" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000413" SignalName="COM1 State" SignalCategory="2" SignalType="1" ChannelNo="44" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="18" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000414" SignalName="COM2 State" SignalCategory="2" SignalType="1" ChannelNo="45" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="19" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000415" SignalName="COM3 State" SignalCategory="2" SignalType="1" ChannelNo="46" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="20" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000416" SignalName="COM4 State" SignalCategory="2" SignalType="1" ChannelNo="47" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="21" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000417" SignalName="EEPROM State" SignalCategory="2" SignalType="1" ChannelNo="48" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="22" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000418" SignalName="THS State" SignalCategory="2" SignalType="1" ChannelNo="49" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="23" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000419" SignalName="Watchdog State" SignalCategory="2" SignalType="1" ChannelNo="50" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="24" SignalProperty="27" SignalMeanings="0:Normal;1:Abnormal" />
      <Signal SignalId="755000420" SignalName="With E1 Network" SignalCategory="2" SignalType="1" ChannelNo="53" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="25" SignalProperty="27" SignalMeanings="0:No;1:Yes" />
      <Signal SignalId="755000421" SignalName="CPU Utilization" SignalCategory="1" SignalType="1" ChannelNo="54" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="%" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="26" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000422" SignalName="FLASH Utilization" SignalCategory="1" SignalType="1" ChannelNo="55" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="%" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="27" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000423" SignalName="SDRAM Utilization" SignalCategory="1" SignalType="1" ChannelNo="56" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="%" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="28" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000424" SignalName="Network Flow kbyte" SignalCategory="1" SignalType="1" ChannelNo="57" ChannelType="1" Expression=" " DataType="" ShowPrecision="0" Unit="kbyte" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="29" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755000425" SignalName="Code Information" SignalCategory="1" SignalType="1" ChannelNo="58" ChannelType="1" Expression=" " DataType="1" ShowPrecision="0" Unit=" " StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="30" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="755001002" SignalName="Monitoring Unit Communication State" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChangeStoreInterval="" ChargeAbsValue="" DisplayIndex="31" SignalProperty="" SignalMeanings="0:Disconnected;1:Connected" />
    </Signals>
    <Events Name="Template Event">
      <Event EventId="755001002" EventName="Monitoring Unit Communication State" EventCategory="7" StartType="2" EndType="3" StartExpression="" SuppressExpression=" " SignalId="0" Enable="True" Visible="True" Description=" " DisplayIndex="26">
          <Conditions>
              <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="Communication Fail" EquipmentState="" BaseTypeId="1301304001" StandardName="0" />
          </Conditions>
     </Event>
    </Events>
    <Controls Name="Template Control" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="755000014" SamplerName="IPLU-HOST Equipment" SamplerType="23" ProtocolCode="IPLU-HOST Equipment 6-00" DllCode="                                " DLLVersion=" " ProtocolFilePath=" " DLLFilePath=" " DllPath="IDUHOST.so" Setting="9600,n,8,1" Description=" " />
  </Samplers>
</EquipmentTemplates>
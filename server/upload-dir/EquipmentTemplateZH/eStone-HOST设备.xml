<?xml version="1.0" encoding="utf-8"?>
<EquipmentTemplates Name="设备模板列表">
  <EquipmentTemplate EquipmentTemplateId="755000028" ParentTemplateId="0" EquipmentTemplateName="eStone-HOST设备" ProtocolCode="40F92E6C4FD30BC02C1F6F656BF84BBC" EquipmentCategory="99" EquipmentType="1" Memo="2024-05-31 14:44:12:首次导入模板" Property="1/3" Decription="" EquipmentStyle="" Unit="" Vendor="" EquipmentBaseType="1301" StationCategory="0">
    <Signals Name="模板信号">
      <Signal SignalId="510001001" SignalName="监控单元通讯状态" SignalCategory="2" SignalType="2" ChannelNo="-3" ChannelType="2" Expression=" " DataType="" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="False" Discription=" " BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="1" SignalProperty="" SignalMeanings="0:通讯异常;1:通讯正常" />
      <Signal SignalId="510000551" SignalName="CPU占用率" SignalCategory="1" SignalType="1" ChannelNo="54" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="%" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="10" MDBSignalId="510000551" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000561" SignalName="FLASH占用率" SignalCategory="1" SignalType="1" ChannelNo="55" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="%" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="11" MDBSignalId="510000561" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000130" SignalName="LOF-A状态" SignalCategory="2" SignalType="1" ChannelNo="12" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="6" MDBSignalId="510000130" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:失步告警" />
      <Signal SignalId="510000150" SignalName="LOF-B状态" SignalCategory="2" SignalType="1" ChannelNo="14" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="8" MDBSignalId="510000150" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:失步告警" />
      <Signal SignalId="510000120" SignalName="LOS-A状态" SignalCategory="2" SignalType="1" ChannelNo="11" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="5" MDBSignalId="510000120" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:断线告警" />
      <Signal SignalId="510000140" SignalName="LOS-B状态" SignalCategory="2" SignalType="1" ChannelNo="13" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="7" MDBSignalId="510000140" ModuleNo="0" SignalProperty="27" SignalMeanings="0:正常;1:断线告警" />
      <Signal SignalId="510000571" SignalName="SDRAM占用率" SignalCategory="1" SignalType="1" ChannelNo="56" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="%" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="12" MDBSignalId="510000571" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000540" SignalName="是否有E1网络" SignalCategory="2" SignalType="1" ChannelNo="53" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="9" MDBSignalId="510000540" ModuleNo="0" SignalProperty="27" SignalMeanings="0:无E1网络;1:有E1网络" />
      <Signal SignalId="510000591" SignalName="条码信息" SignalCategory="1" SignalType="1" ChannelNo="58" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="14" MDBSignalId="510000591" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
      <Signal SignalId="510000000" SignalName="网口工作模式" SignalCategory="2" SignalType="1" ChannelNo="10" ChannelType="2" Expression="" DataType="0" ShowPrecision="" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="2" MDBSignalId="510000000" ModuleNo="0" SignalProperty="27" SignalMeanings="0:半双工;1:全双工" />
      <Signal SignalId="510000090" SignalName="网口连接状态" SignalCategory="2" SignalType="1" ChannelNo="8" ChannelType="2" Expression="" DataType="0" ShowPrecision="0" Unit="" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="4" MDBSignalId="510000090" ModuleNo="0" SignalProperty="27" SignalMeanings="0:中断;1:正常" />
      <Signal SignalId="510000010" SignalName="网口连接速度" SignalCategory="2" SignalType="1" ChannelNo="9" ChannelType="2" Expression="" DataType="0" ShowPrecision="" Unit="" StoreInterval="0" AbsValueThreshold="1" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="3" MDBSignalId="510000010" ModuleNo="0" SignalProperty="27" SignalMeanings="0:10M;1:100M" />
      <Signal SignalId="510000581" SignalName="网络流量kbyte" SignalCategory="1" SignalType="1" ChannelNo="57" ChannelType="1" Expression="" DataType="0" ShowPrecision="0" Unit="kbyte" StoreInterval="0" AbsValueThreshold="0" PercentThreshold="" StaticsPeriod="" Enable="True" Visible="True" Discription="" BaseTypeId="" ChargeStoreInterVal="" ChargeAbsValue="" DisplayIndex="13" MDBSignalId="510000581" ModuleNo="0" SignalProperty="27" SignalMeanings="" />
    </Signals>
    <Events Name="模板事件">
      <Event EventId="510001001" EventName="监控单元通讯状态" EventCategory="7" StartType="2" EndType="3" StartExpression="" SuppressExpression=" " SignalId="0" Enable="True" Visible="True" Description=" " DisplayIndex="1">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation=" " EndCompareValue="0" EndDelay="0" Frequency="0" FrequencyThreshold="0" Meanings="通讯异常" EquipmentState="" BaseTypeId="1301310001" StandardName="0" />
        </Conditions>
      </Event>
      <Event EventId="510000090" EventName="网口连接状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000090]" SuppressExpression="" SignalId="510000090" Enable="True" Visible="True" Description="" DisplayIndex="4" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="0" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="中断" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000120" EventName="LOS-A状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000120]" SuppressExpression="" SignalId="510000120" Enable="True" Visible="True" Description="" DisplayIndex="5" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="断线告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000130" EventName="LOF-A状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000130]" SuppressExpression="" SignalId="510000130" Enable="True" Visible="True" Description="" DisplayIndex="6" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="失步告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000140" EventName="LOS-B状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000140]" SuppressExpression="" SignalId="510000140" Enable="True" Visible="True" Description="" DisplayIndex="7" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="断线告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
      <Event EventId="510000150" EventName="LOF-B状态" EventCategory="2" StartType="1" EndType="3" StartExpression="[-1,510000150]" SuppressExpression="" SignalId="510000150" Enable="True" Visible="True" Description="" DisplayIndex="8" ModuleNo="0">
        <Conditions>
          <EventCondition EventConditionId="0" EventSeverity="1" StartOperation="=" StartCompareValue="1" StartDelay="0" EndOperation="" EndCompareValue="" EndDelay="" Frequency="" FrequencyThreshold="" Meanings="失步告警" EquipmentState="2" BaseTypeId="" StandardName="" />
        </Conditions>
      </Event>
    </Events>
    <Controls Name="模板控制" />
  </EquipmentTemplate>
  <Samplers>
    <Sampler SamplerId="755000030" SamplerName="eStone-HOST设备" SamplerType="18" ProtocolCode="40F92E6C4FD30BC02C1F6F656BF84BBC" DllCode="" DLLVersion="" ProtocolFilePath="" DLLFilePath="" DllPath="eStoneHOST.so" Setting="9600,n,8,1" Description="" />
  </Samplers>
</EquipmentTemplates>
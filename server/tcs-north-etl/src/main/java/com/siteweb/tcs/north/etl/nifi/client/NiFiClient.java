package com.siteweb.tcs.north.etl.nifi.client;

import com.siteweb.tcs.north.etl.nifi.config.NiFiConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager;
import org.apache.hc.client5.http.socket.ConnectionSocketFactory;
import org.apache.hc.client5.http.socket.PlainConnectionSocketFactory;
import org.apache.hc.client5.http.ssl.NoopHostnameVerifier;
import org.apache.hc.client5.http.ssl.SSLConnectionSocketFactory;
import org.apache.hc.core5.http.config.Registry;
import org.apache.hc.core5.http.config.RegistryBuilder;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.nifi.web.api.dto.*;
import org.apache.nifi.web.api.entity.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.net.ssl.SSLContext;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * NiFi客户端
 * 封装对NiFi REST API的调用
 */
@Slf4j
@Component
public class NiFiClient {

    private final NiFiConfig nifiConfig;
    private final RestTemplate restTemplate;
    private String token;

    @Autowired
    public NiFiClient(NiFiConfig nifiConfig) {
        this.nifiConfig = nifiConfig;
        this.restTemplate = createRestTemplate();
    }

    /**
     * 创建RestTemplate
     */
    private RestTemplate createRestTemplate() {
        try {
            HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory();
            requestFactory.setConnectTimeout(nifiConfig.getConnectTimeout());
            requestFactory.setConnectionRequestTimeout(nifiConfig.getReadTimeout());
            
            CloseableHttpClient httpClient;
            
            if (nifiConfig.isUseSSL() && nifiConfig.isTrustAllCerts()) {
                // 配置SSL，信任所有证书
                SSLContext sslContext = new SSLContextBuilder()
                        .loadTrustMaterial(null, (x509Certificates, s) -> true)
                        .build();
                
                SSLConnectionSocketFactory sslSocketFactory = new SSLConnectionSocketFactory(
                        sslContext, NoopHostnameVerifier.INSTANCE);
                
                Registry<ConnectionSocketFactory> socketFactoryRegistry = RegistryBuilder.<ConnectionSocketFactory>create()
                        .register("http", PlainConnectionSocketFactory.getSocketFactory())
                        .register("https", sslSocketFactory)
                        .build();
                
                PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(socketFactoryRegistry);
                httpClient = HttpClients.custom()
                        .setConnectionManager(connectionManager)
                        .build();
            } else {
                // 标准HTTP客户端
                httpClient = HttpClients.createDefault();
            }
            
            requestFactory.setHttpClient(httpClient);
            return new RestTemplate(requestFactory);
        } catch (NoSuchAlgorithmException | KeyStoreException | KeyManagementException e) {
            log.error("Error creating RestTemplate for NiFi client", e);
            throw new RuntimeException("Failed to create NiFi client", e);
        }
    }

    /**
     * 获取访问令牌
     */
    public String getToken() {
        if (token != null) {
            return token;
        }
        
        if (nifiConfig.getToken() != null && !nifiConfig.getToken().isEmpty()) {
            token = nifiConfig.getToken();
            return token;
        }
        
        if (nifiConfig.getUsername() != null && nifiConfig.getPassword() != null) {
            // 实现用户名密码认证获取令牌的逻辑
            // 这里需要根据NiFi的认证方式进行实现
            log.info("Authenticating with NiFi using username and password");
            // TODO: 实现认证逻辑
        }
        
        return token;
    }

    /**
     * 创建HTTP头
     */
    private HttpHeaders createHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));
        
        String token = getToken();
        if (token != null && !token.isEmpty()) {
            headers.setBearerAuth(token);
        }
        
        return headers;
    }

    /**
     * 获取流程组信息
     */
    public ProcessGroupEntity getProcessGroup(String processGroupId) {
        String url = nifiConfig.getBaseUrl() + "/process-groups/" + processGroupId;
        return restTemplate.getForObject(url, ProcessGroupEntity.class);
    }

    /**
     * 创建流程组
     */
    public ProcessGroupEntity createProcessGroup(String parentGroupId, String name, String position) {
        String url = nifiConfig.getBaseUrl() + "/process-groups/" + parentGroupId + "/process-groups";
        
        // 创建请求体
        ProcessGroupEntity entity = new ProcessGroupEntity();
        ProcessGroupDTO component = new ProcessGroupDTO();
        component.setName(name);
        
        // 设置位置
        Map<String, String> posMap = new HashMap<>();
        posMap.put("x", "0");
        posMap.put("y", "0");
        component.setPosition(posMap);
        
        entity.setComponent(component);
        entity.setRevision(createRevision());
        
        return restTemplate.postForObject(url, entity, ProcessGroupEntity.class);
    }

    /**
     * 创建处理器
     */
    public ProcessorEntity createProcessor(String processGroupId, String type, String name, Map<String, String> properties) {
        String url = nifiConfig.getBaseUrl() + "/process-groups/" + processGroupId + "/processors";
        
        // 创建请求体
        ProcessorEntity entity = new ProcessorEntity();
        ProcessorDTO component = new ProcessorDTO();
        component.setType(type);
        component.setName(name);
        
        // 设置位置
        Map<String, String> posMap = new HashMap<>();
        posMap.put("x", "0");
        posMap.put("y", "0");
        component.setPosition(posMap);
        
        // 设置属性
        component.setProperties(properties);
        
        entity.setComponent(component);
        entity.setRevision(createRevision());
        
        return restTemplate.postForObject(url, entity, ProcessorEntity.class);
    }

    /**
     * 创建连接
     */
    public ConnectionEntity createConnection(String processGroupId, String sourceId, String destinationId,
                                            String sourceType, String destinationType,
                                            String sourcePort, String destinationPort) {
        String url = nifiConfig.getBaseUrl() + "/process-groups/" + processGroupId + "/connections";
        
        // 创建请求体
        ConnectionEntity entity = new ConnectionEntity();
        ConnectionDTO component = new ConnectionDTO();
        
        // 设置源端点
        ConnectableDTO source = new ConnectableDTO();
        source.setId(sourceId);
        source.setType(sourceType);
        source.setGroupId(processGroupId);
        if (sourcePort != null) {
            source.setName(sourcePort);
        }
        component.setSource(source);
        
        // 设置目标端点
        ConnectableDTO destination = new ConnectableDTO();
        destination.setId(destinationId);
        destination.setType(destinationType);
        destination.setGroupId(processGroupId);
        if (destinationPort != null) {
            destination.setName(destinationPort);
        }
        component.setDestination(destination);
        
        // 设置关系
        component.setSelectedRelationships(Set.of("success"));
        
        entity.setComponent(component);
        entity.setRevision(createRevision());
        
        return restTemplate.postForObject(url, entity, ConnectionEntity.class);
    }

    /**
     * 启动流程组
     */
    public ProcessGroupEntity startProcessGroup(String processGroupId) {
        String url = nifiConfig.getBaseUrl() + "/flow/process-groups/" + processGroupId;
        
        // 创建请求体
        ScheduleComponentsEntity entity = new ScheduleComponentsEntity();
        entity.setId(processGroupId);
        entity.setState("RUNNING");
        
        return restTemplate.putForObject(url, entity, ProcessGroupEntity.class);
    }

    /**
     * 停止流程组
     */
    public ProcessGroupEntity stopProcessGroup(String processGroupId) {
        String url = nifiConfig.getBaseUrl() + "/flow/process-groups/" + processGroupId;
        
        // 创建请求体
        ScheduleComponentsEntity entity = new ScheduleComponentsEntity();
        entity.setId(processGroupId);
        entity.setState("STOPPED");
        
        return restTemplate.putForObject(url, entity, ProcessGroupEntity.class);
    }

    /**
     * 获取流程组状态
     */
    public ProcessGroupStatusEntity getProcessGroupStatus(String processGroupId) {
        String url = nifiConfig.getBaseUrl() + "/flow/process-groups/" + processGroupId + "/status";
        return restTemplate.getForObject(url, ProcessGroupStatusEntity.class);
    }

    /**
     * 获取集群状态
     */
    public ClusterEntity getClusterStatus() {
        String url = nifiConfig.getBaseUrl() + "/controller/cluster";
        return restTemplate.getForObject(url, ClusterEntity.class);
    }

    /**
     * 获取公告
     */
    public BulletinBoardEntity getBulletins() {
        String url = nifiConfig.getBaseUrl() + "/flow/bulletin-board";
        return restTemplate.getForObject(url, BulletinBoardEntity.class);
    }

    /**
     * 创建版本信息
     */
    private RevisionDTO createRevision() {
        RevisionDTO revision = new RevisionDTO();
        revision.setVersion(0L);
        return revision;
    }
}

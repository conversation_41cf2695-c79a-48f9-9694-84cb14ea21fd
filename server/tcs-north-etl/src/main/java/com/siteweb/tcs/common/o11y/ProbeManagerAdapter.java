package com.siteweb.tcs.common.o11y;

/**
 * Stub implementation of ProbeManagerAdapter for compilation purposes.
 * This is a simplified version of the original class.
 */
public class ProbeManagerAdapter {
    
    public static final String TRACE_GRAPH_FIELD = "traceGraph";
    public static final String TRACE_SPAN_FIELD = "traceSpan";
    
    /**
     * Creates a new trace graph.
     * 
     * @param name The name of the trace graph
     * @return The trace graph
     */
    public static TraceGraph createTraceGraph(String name) {
        return new TraceGraph(name);
    }
    
    /**
     * Creates a new trace span.
     * 
     * @param name The name of the trace span
     * @return The trace span
     */
    public static TraceSpan createTraceSpan(String name) {
        return new TraceSpan(name);
    }
    
    /**
     * Starts a trace span.
     * 
     * @param span The trace span to start
     */
    public static void startTraceSpan(TraceSpan span) {
        // Stub implementation
    }
    
    /**
     * Ends a trace span.
     * 
     * @param span The trace span to end
     */
    public static void endTraceSpan(TraceSpan span) {
        // Stub implementation
    }
    
    /**
     * Adds a tag to a trace span.
     * 
     * @param span The trace span
     * @param key The tag key
     * @param value The tag value
     */
    public static void addTraceSpanTag(TraceSpan span, String key, String value) {
        // Stub implementation
    }
    
    /**
     * Adds an event to a trace span.
     * 
     * @param span The trace span
     * @param name The event name
     */
    public static void addTraceSpanEvent(TraceSpan span, String name) {
        // Stub implementation
    }
    
    /**
     * Adds a trace span to a trace graph.
     * 
     * @param graph The trace graph
     * @param span The trace span
     */
    public static void addTraceSpanToGraph(TraceGraph graph, TraceSpan span) {
        // Stub implementation
    }
}

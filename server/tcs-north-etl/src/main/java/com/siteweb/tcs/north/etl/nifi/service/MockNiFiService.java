package com.siteweb.tcs.north.etl.nifi.service;

import com.siteweb.tcs.north.etl.nifi.client.MockNiFiClient;
import com.siteweb.tcs.north.etl.nifi.config.NiFiConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Primary;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 模拟NiFi服务
 * 提供NiFi操作的模拟实现
 */
@Slf4j
@Service
@Primary
@ConditionalOnProperty(name = "plugin.etl.nifi.mock-enabled", havingValue = "true")
public class MockNiFiService {

    private final MockNiFiClient mockNiFiClient;
    private final NiFiConfig nifiConfig;

    @Autowired
    public MockNiFiService(MockNiFiClient mockNiFiClient, NiFiConfig nifiConfig) {
        this.mockNiFiClient = mockNiFiClient;
        this.nifiConfig = nifiConfig;
        log.info("Initializing Mock NiFi Service");
    }

    /**
     * 创建ETL流程组
     *
     * @param name 流程组名称
     * @return 流程组ID
     */
    public String createEtlProcessGroup(String name) {
        try {
            log.info("Mock: Creating ETL process group: {}", name);
            Map<String, Object> processGroup = (Map<String, Object>) mockNiFiClient.createProcessGroup(
                    nifiConfig.getRootProcessGroupId(), name, "0,0");
            String id = (String) processGroup.get("id");
            log.info("Mock: Created ETL process group: {} with ID: {}", name, id);
            return id;
        } catch (Exception e) {
            log.error("Mock: Error creating ETL process group: {}", name, e);
            throw new RuntimeException("Failed to create ETL process group", e);
        }
    }

    /**
     * 创建数据库查询处理器
     *
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param databaseType 数据库类型
     * @param connectionUrl 连接URL
     * @param username 用户名
     * @param password 密码
     * @param query SQL查询
     * @return 处理器ID
     */
    public String createDatabaseQueryProcessor(String processGroupId, String name, 
                                              String databaseType, String connectionUrl, 
                                              String username, String password, String query) {
        try {
            log.info("Mock: Creating database query processor: {}", name);

            Map<String, String> properties = new HashMap<>();
            properties.put("Database Connection Pooling Service", "dbcp-service");
            properties.put("Database Type", databaseType);
            properties.put("Connection URL", connectionUrl);
            properties.put("Database Driver Class Name", getDatabaseDriverClassName(databaseType));
            properties.put("Database User", username);
            properties.put("Password", password);
            properties.put("SQL select query", query);

            Map<String, Object> processor = (Map<String, Object>) mockNiFiClient.createProcessor(
                    processGroupId,
                    "org.apache.nifi.processors.standard.QueryDatabaseTable",
                    name,
                    properties);

            String id = (String) processor.get("id");
            log.info("Mock: Created database query processor with ID: {}", id);
            return id;
        } catch (Exception e) {
            log.error("Mock: Error creating database query processor: {}", name, e);
            throw new RuntimeException("Failed to create database query processor", e);
        }
    }

    /**
     * 创建文件写入处理器
     *
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param directory 目录
     * @param filename 文件名
     * @return 处理器ID
     */
    public String createFileWriteProcessor(String processGroupId, String name, String directory, String filename) {
        try {
            log.info("Mock: Creating file write processor: {}", name);

            Map<String, String> properties = new HashMap<>();
            properties.put("Directory", directory);
            properties.put("Filename", filename);

            Map<String, Object> processor = (Map<String, Object>) mockNiFiClient.createProcessor(
                    processGroupId,
                    "org.apache.nifi.processors.standard.PutFile",
                    name,
                    properties);

            String id = (String) processor.get("id");
            log.info("Mock: Created file write processor with ID: {}", id);
            return id;
        } catch (Exception e) {
            log.error("Mock: Error creating file write processor: {}", name, e);
            throw new RuntimeException("Failed to create file write processor", e);
        }
    }

    /**
     * 创建数据库写入处理器
     *
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param databaseType 数据库类型
     * @param connectionUrl 连接URL
     * @param username 用户名
     * @param password 密码
     * @param tableName 表名
     * @return 处理器ID
     */
    public String createDatabaseWriteProcessor(String processGroupId, String name,
                                              String databaseType, String connectionUrl,
                                              String username, String password, String tableName) {
        try {
            log.info("Mock: Creating database write processor: {}", name);

            Map<String, String> properties = new HashMap<>();
            properties.put("Database Connection Pooling Service", "dbcp-service");
            properties.put("Database Type", databaseType);
            properties.put("Connection URL", connectionUrl);
            properties.put("Database Driver Class Name", getDatabaseDriverClassName(databaseType));
            properties.put("Database User", username);
            properties.put("Password", password);
            properties.put("Table Name", tableName);
            properties.put("Statement Type", "INSERT");

            Map<String, Object> processor = (Map<String, Object>) mockNiFiClient.createProcessor(
                    processGroupId,
                    "org.apache.nifi.processors.standard.PutDatabaseRecord",
                    name,
                    properties);

            String id = (String) processor.get("id");
            log.info("Mock: Created database write processor with ID: {}", id);
            return id;
        } catch (Exception e) {
            log.error("Mock: Error creating database write processor: {}", name, e);
            throw new RuntimeException("Failed to create database write processor", e);
        }
    }

    /**
     * 创建转换处理器
     *
     * @param processGroupId 流程组ID
     * @param name 处理器名称
     * @param scriptType 脚本类型
     * @param scriptBody 脚本内容
     * @return 处理器ID
     */
    public String createTransformProcessor(String processGroupId, String name, String scriptType, String scriptBody) {
        try {
            log.info("Mock: Creating transform processor: {}", name);

            Map<String, String> properties = new HashMap<>();
            properties.put("Script Engine", scriptType);
            properties.put("Script Body", scriptBody);

            Map<String, Object> processor = (Map<String, Object>) mockNiFiClient.createProcessor(
                    processGroupId,
                    "org.apache.nifi.processors.script.ExecuteScript",
                    name,
                    properties);

            String id = (String) processor.get("id");
            log.info("Mock: Created transform processor with ID: {}", id);
            return id;
        } catch (Exception e) {
            log.error("Mock: Error creating transform processor: {}", name, e);
            throw new RuntimeException("Failed to create transform processor", e);
        }
    }

    /**
     * 创建连接
     *
     * @param processGroupId 流程组ID
     * @param sourceId 源处理器ID
     * @param destinationId 目标处理器ID
     * @return 连接ID
     */
    public String createConnection(String processGroupId, String sourceId, String destinationId) {
        try {
            log.info("Mock: Creating connection between {} and {}", sourceId, destinationId);

            Map<String, Object> connection = (Map<String, Object>) mockNiFiClient.createConnection(
                    processGroupId,
                    sourceId,
                    destinationId,
                    "PROCESSOR",
                    "PROCESSOR",
                    null,
                    null);

            String id = (String) connection.get("id");
            log.info("Mock: Created connection with ID: {}", id);
            return id;
        } catch (Exception e) {
            log.error("Mock: Error creating connection between {} and {}", sourceId, destinationId, e);
            throw new RuntimeException("Failed to create connection", e);
        }
    }

    /**
     * 启动流程组
     *
     * @param processGroupId 流程组ID
     * @return 是否成功
     */
    public boolean startProcessGroup(String processGroupId) {
        try {
            log.info("Mock: Starting process group: {}", processGroupId);
            mockNiFiClient.startProcessGroup(processGroupId);
            return true;
        } catch (Exception e) {
            log.error("Mock: Error starting process group: {}", processGroupId, e);
            return false;
        }
    }

    /**
     * 停止流程组
     *
     * @param processGroupId 流程组ID
     * @return 是否成功
     */
    public boolean stopProcessGroup(String processGroupId) {
        try {
            log.info("Mock: Stopping process group: {}", processGroupId);
            mockNiFiClient.stopProcessGroup(processGroupId);
            return true;
        } catch (Exception e) {
            log.error("Mock: Error stopping process group: {}", processGroupId, e);
            return false;
        }
    }

    /**
     * 获取集群状态
     *
     * @return 集群状态
     */
    @Cacheable(value = "clusterStatus")
    public Map<String, Object> getClusterStatus() {
        try {
            log.info("Mock: Getting cluster status");
            Map<String, Object> status = new HashMap<>();
            status.put("id", "mock-cluster");
            status.put("status", "CONNECTED");
            status.put("nodes", new ArrayList<>());
            return status;
        } catch (Exception e) {
            log.error("Mock: Error getting cluster status", e);
            Map<String, Object> errorStatus = new HashMap<>();
            errorStatus.put("status", "ERROR");
            errorStatus.put("error", e.getMessage());
            return errorStatus;
        }
    }

    /**
     * 清除缓存
     */
    @Scheduled(fixedDelayString = "${plugin.etl.nifi.polling-interval:5000}")
    @CacheEvict(value = {"processGroups", "processors", "connections", "clusterStatus", "processGroupStatus"}, allEntries = true)
    public void clearCache() {
        log.debug("Mock: Clearing cache");
    }

    /**
     * 根据数据库类型获取驱动类名
     *
     * @param databaseType 数据库类型
     * @return 驱动类名
     */
    private String getDatabaseDriverClassName(String databaseType) {
        switch (databaseType.toLowerCase()) {
            case "mysql":
                return "com.mysql.cj.jdbc.Driver";
            case "postgresql":
                return "org.postgresql.Driver";
            case "oracle":
                return "oracle.jdbc.driver.OracleDriver";
            case "sqlserver":
                return "com.microsoft.sqlserver.jdbc.SQLServerDriver";
            case "h2":
                return "org.h2.Driver";
            default:
                return "com.mysql.cj.jdbc.Driver";
        }
    }
}

package com.siteweb.tcs.north.etl.repository;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.north.etl.model.Task;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务数据访问接口
 */
@Mapper
public interface TaskRepository extends BaseMapper<Task> {
    
    /**
     * 根据策略ID查询任务列表
     */
    @Select("SELECT * FROM etl_task WHERE strategy_id = #{strategyId}")
    List<Task> findByStrategyId(@Param("strategyId") Integer strategyId);
    
    /**
     * 根据状态查询任务列表
     */
    @Select("SELECT * FROM etl_task WHERE status = #{status}")
    List<Task> findByStatus(@Param("status") String status);
    
    /**
     * 查询所有需要调度的任务
     */
    @Select("SELECT * FROM etl_task WHERE schedule_cron IS NOT NULL AND schedule_cron != ''")
    List<Task> findScheduledTasks();
}

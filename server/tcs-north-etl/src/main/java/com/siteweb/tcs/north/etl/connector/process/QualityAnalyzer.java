package com.siteweb.tcs.north.etl.connector.process;

import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.etl.domain.letter.DataCleaningMessage;
import lombok.extern.slf4j.Slf4j;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 质量分析器
 * 负责分析数据质量
 */
@Slf4j
public class QualityAnalyzer extends AbstractActor {

    private final ActorProbe probe;
    private final ActorRef dataCleaningStore;
    
    /**
     * 构造函数
     */
    public QualityAnalyzer(ActorRef dataCleaningStore) {
        this.dataCleaningStore = dataCleaningStore;
        this.probe = createProbe(this);
        probe.addCounter("QualityIssueCounter");
    }
    
    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DataCleaningMessage.class, this::handleDataCleaning)
                .matchAny(this::unhandled)
                .build();
    }
    
    /**
     * 处理数据处理消息
     */
    private void handleDataCleaning(DataCleaningMessage message) {
        probe.info("Analyzing data quality: " + message.getWindowLogString());
        
        try {
            // 分析数据质量
            analyzeQuality(message);
            
            // 更新计数器
            probe.incrementCounterAmount("QualityIssueCounter", 1);
        } catch (Exception e) {
            probe.error("Error analyzing data quality: " + e.getMessage());
        }
    }
    
    /**
     * 分析数据质量
     */
    private void analyzeQuality(DataCleaningMessage message) {
        // 实现数据质量分析逻辑
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
    
    /**
     * 创建Props
     */
    public static Props props(ActorRef dataCleaningStore) {
        return Props.create(QualityAnalyzer.class, dataCleaningStore);
    }
}

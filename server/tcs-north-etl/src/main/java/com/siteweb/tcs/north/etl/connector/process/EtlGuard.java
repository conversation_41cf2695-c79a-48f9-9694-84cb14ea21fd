package com.siteweb.tcs.north.etl.connector.process;

import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.actions.SubscribeAction;
import com.siteweb.tcs.common.actions.UnsubscribeAction;
import com.siteweb.tcs.common.o11y.ActorLogItem;
import com.siteweb.tcs.common.o11y.ActorLogLevel;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.o11y.TraceGraph;
import com.siteweb.tcs.common.o11y.TraceGraphManager;
import com.siteweb.tcs.common.o11y.FlowGraph;
import com.siteweb.tcs.common.o11y.Node;
import com.siteweb.tcs.common.o11y.NodeType;
import com.siteweb.tcs.common.o11y.Connection;
import com.siteweb.tcs.north.etl.domain.letter.CleaningStrategyMessage;
import com.siteweb.tcs.north.etl.domain.letter.DataCleaningMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.ArrayList;
import java.util.List;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * ETL数据处理根Actor
 * 负责管理数据的提取、转换和加载过程
 */
@Slf4j
public class EtlGuard extends AbstractActor {

    private final ActorProbe probe;
    private final RedisTemplate<String, Object> redisTemplate;
    private final List<ActorRef> subscribers = new ArrayList<>();
    private TraceGraph graph;
    
    // 子Actor引用
    private final ActorRef dataCleaningAdapter;
    private final ActorRef dataCleaningStore;
    private final ActorRef dataCleaningSpout;
    private final ActorRef strategyManager;
    private final ActorRef qualityAnalyzer;
    
    // 日志队列名称
    private static final String CLEANING_LOG = "EtlLog";
    private static final String STRATEGY_LOG = "StrategyLog";
    private static final String QUALITY_LOG = "QualityLog";
    
    /**
     * 构造函数
     */
    public EtlGuard(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
        this.probe = createProbe(this);
        
        // 初始化日志队列
        probe.addWindowLog(CLEANING_LOG);
        probe.addWindowLog(STRATEGY_LOG);
        probe.addWindowLog(QUALITY_LOG);
        
        // 初始化计数器
        probe.addCounter("DataProcessingCounter");
        probe.addCounter("StrategyCounter");
        probe.addCounter("QualityIssueCounter");
        
        // 创建子Actor
        dataCleaningSpout = getContext().actorOf(
                Props.create(DataCleaningSpout.class),
                "DataCleaningSpout"
        );
        
        dataCleaningStore = getContext().actorOf(
                Props.create(DataCleaningStore.class, dataCleaningSpout),
                "DataCleaningStore"
        );
        
        dataCleaningAdapter = getContext().actorOf(
                Props.create(DataCleaningAdapter.class, dataCleaningStore),
                "DataCleaningAdapter"
        );
        
        strategyManager = getContext().actorOf(
                Props.create(StrategyManager.class, dataCleaningAdapter),
                "StrategyManager"
        );
        
        qualityAnalyzer = getContext().actorOf(
                Props.create(QualityAnalyzer.class, dataCleaningStore),
                "QualityAnalyzer"
        );
        
        // 创建跟踪图
        createTraceGraph();
        
        // 日志记录
        probe.info("EtlGuard initialized successfully");
    }
    
    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(SubscribeAction.class, this::handleSubscribe)
                .match(UnsubscribeAction.class, this::handleUnsubscribe)
                .match(DataCleaningMessage.class, this::handleDataCleaning)
                .match(CleaningStrategyMessage.class, this::handleCleaningStrategy)
                .matchAny(this::unhandled)
                .build();
    }
    
    /**
     * 处理订阅请求
     */
    private void handleSubscribe(SubscribeAction action) {
        if (!subscribers.contains(action.getSubscriber())) {
            subscribers.add(action.getSubscriber());
            probe.info("New subscriber registered: " + action.getSubscriber().path().name());
        }
    }
    
    /**
     * 处理取消订阅请求
     */
    private void handleUnsubscribe(UnsubscribeAction action) {
        subscribers.remove(action.getSubscriber());
        probe.info("Subscriber unregistered: " + action.getSubscriber().path().name());
    }
    
    /**
     * 处理数据处理消息
     */
    private void handleDataCleaning(DataCleaningMessage message) {
        probe.enqueueWindowLogItem(CLEANING_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Received data processing request: " + message.toString()));
        
        // 转发到数据适配器
        dataCleaningAdapter.tell(message, getSelf());
        
        // 更新计数器
        probe.incrementCounterAmount("DataProcessingCounter", 1);
    }
    
    /**
     * 处理处理策略消息
     */
    private void handleCleaningStrategy(CleaningStrategyMessage message) {
        probe.enqueueWindowLogItem(STRATEGY_LOG, new ActorLogItem(ActorLogLevel.INFO,
                "Received processing strategy: " + message.toString()));
        
        // 转发到策略管理器
        strategyManager.tell(message, getSelf());
        
        // 更新计数器
        probe.incrementCounterAmount("StrategyCounter", 1);
    }
    
    /**
     * 创建跟踪图
     */
    private void createTraceGraph() {
        graph = new TraceGraph("etl-processor", "north-etl-plugin", "north-plugin", "north-etl-plugin");
        TraceGraphManager.addGraph(graph);
        
        // 创建数据处理流程图
        FlowGraph flowGraph = new FlowGraph("ETL数据处理流程");
        
        Node etlGuard = new Node("EtlGuard", NodeType.START, "ETL处理根Actor", self());
        flowGraph.addNode(etlGuard);
        
        Node dataCleaningAdapterNode = new Node("DataCleaningAdapter", NodeType.PROCESS, "数据处理适配器", dataCleaningAdapter);
        flowGraph.addNode(dataCleaningAdapterNode);
        
        Node dataCleaningStoreNode = new Node("DataCleaningStore", NodeType.PROCESS, "数据处理存储", dataCleaningStore);
        flowGraph.addNode(dataCleaningStoreNode);
        
        Node dataCleaningSpoutNode = new Node("DataCleaningSpout", NodeType.PROCESS, "数据处理发送器", dataCleaningSpout);
        flowGraph.addNode(dataCleaningSpoutNode);
        
        Node strategyManagerNode = new Node("StrategyManager", NodeType.PROCESS, "策略管理器", strategyManager);
        flowGraph.addNode(strategyManagerNode);
        
        Node qualityAnalyzerNode = new Node("QualityAnalyzer", NodeType.PROCESS, "质量分析器", qualityAnalyzer);
        flowGraph.addNode(qualityAnalyzerNode);
        
        // 添加连接
        Connection connection1 = new Connection("数据流", etlGuard, dataCleaningAdapterNode);
        flowGraph.addConnection(connection1);
        
        Connection connection2 = new Connection("数据流", dataCleaningAdapterNode, dataCleaningStoreNode);
        flowGraph.addConnection(connection2);
        
        Connection connection3 = new Connection("数据流", dataCleaningStoreNode, dataCleaningSpoutNode);
        flowGraph.addConnection(connection3);
        
        Connection connection4 = new Connection("策略流", etlGuard, strategyManagerNode);
        flowGraph.addConnection(connection4);
        
        Connection connection5 = new Connection("策略流", strategyManagerNode, dataCleaningAdapterNode);
        flowGraph.addConnection(connection5);
        
        Connection connection6 = new Connection("质量分析", dataCleaningStoreNode, qualityAnalyzerNode);
        flowGraph.addConnection(connection6);
        
        graph.addFlowGraph(flowGraph);
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        if (graph != null) {
            TraceGraphManager.removeGraph(graph.getId());
        }
        super.postStop();
    }
    
    /**
     * 创建Props
     */
    public static Props props(RedisTemplate<String, Object> redisTemplate) {
        return Props.create(EtlGuard.class, redisTemplate);
    }
}

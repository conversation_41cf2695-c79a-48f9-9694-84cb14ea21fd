package com.siteweb.tcs.north.etl.connector.process;

import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.north.etl.domain.letter.DataCleaningMessage;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

/**
 * 数据处理发送器
 * 负责将处理后的数据发送到目标系统
 */
@Slf4j
public class DataCleaningSpout extends AbstractActor {

    private final ActorProbe probe;
    private final List<DataCleaningMessage> processedMessages = new ArrayList<>();
    
    /**
     * 构造函数
     */
    public DataCleaningSpout() {
        this.probe = createProbe(this);
        probe.addCounter("SentDataCounter");
    }
    
    /**
     * Actor接收消息的处理方法
     */
    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(DataCleaningMessage.class, this::handleDataCleaning)
                .matchAny(this::unhandled)
                .build();
    }
    
    /**
     * 处理数据处理消息
     */
    private void handleDataCleaning(DataCleaningMessage message) {
        probe.info("Sending data: " + message.getWindowLogString());
        
        try {
            // 发送数据
            sendData(message);
            
            // 存储已处理的消息
            processedMessages.add(message);
            
            // 更新计数器
            probe.incrementCounterAmount("SentDataCounter", 1);
        } catch (Exception e) {
            probe.error("Error sending data: " + e.getMessage());
        }
    }
    
    /**
     * 发送数据
     */
    private void sendData(DataCleaningMessage message) {
        // 实现数据发送逻辑
    }
    
    /**
     * Actor停止时的清理工作
     */
    @Override
    public void postStop() {
        removeProbe(probe);
        super.postStop();
    }
    
    /**
     * 创建Props
     */
    public static Props props() {
        return Props.create(DataCleaningSpout.class);
    }
}

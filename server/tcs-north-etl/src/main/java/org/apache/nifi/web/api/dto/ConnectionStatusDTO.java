package org.apache.nifi.web.api.dto;

import java.util.Date;

/**
 * Stub implementation of ConnectionStatusDTO for compilation purposes.
 * This is a simplified version of the original class from Apache NiFi.
 */
public class ConnectionStatusDTO {
    private String id;
    private String groupId;
    private String name;
    private String sourceId;
    private String sourceName;
    private String destinationId;
    private String destinationName;
    private Date statsLastRefreshed;
    private String inputCount;
    private String inputBytes;
    private String queuedCount;
    private String queuedBytes;
    private String outputCount;
    private String outputBytes;
    private String maxQueuedCount;
    private String maxQueuedBytes;
    private String totalQueuedDuration;
    private String maxQueuedDuration;
    private String flowFilesIn;
    private String bytesIn;
    private String flowFilesOut;
    private String bytesOut;
    private String percentUseCount;
    private String percentUseBytes;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getSourceId() {
        return sourceId;
    }

    public void setSourceId(String sourceId) {
        this.sourceId = sourceId;
    }

    public String getSourceName() {
        return sourceName;
    }

    public void setSourceName(String sourceName) {
        this.sourceName = sourceName;
    }

    public String getDestinationId() {
        return destinationId;
    }

    public void setDestinationId(String destinationId) {
        this.destinationId = destinationId;
    }

    public String getDestinationName() {
        return destinationName;
    }

    public void setDestinationName(String destinationName) {
        this.destinationName = destinationName;
    }

    public Date getStatsLastRefreshed() {
        return statsLastRefreshed;
    }

    public void setStatsLastRefreshed(Date statsLastRefreshed) {
        this.statsLastRefreshed = statsLastRefreshed;
    }

    public String getInputCount() {
        return inputCount;
    }

    public void setInputCount(String inputCount) {
        this.inputCount = inputCount;
    }

    public String getInputBytes() {
        return inputBytes;
    }

    public void setInputBytes(String inputBytes) {
        this.inputBytes = inputBytes;
    }

    public String getQueuedCount() {
        return queuedCount;
    }

    public void setQueuedCount(String queuedCount) {
        this.queuedCount = queuedCount;
    }

    public String getQueuedBytes() {
        return queuedBytes;
    }

    public void setQueuedBytes(String queuedBytes) {
        this.queuedBytes = queuedBytes;
    }

    public String getOutputCount() {
        return outputCount;
    }

    public void setOutputCount(String outputCount) {
        this.outputCount = outputCount;
    }

    public String getOutputBytes() {
        return outputBytes;
    }

    public void setOutputBytes(String outputBytes) {
        this.outputBytes = outputBytes;
    }

    public String getMaxQueuedCount() {
        return maxQueuedCount;
    }

    public void setMaxQueuedCount(String maxQueuedCount) {
        this.maxQueuedCount = maxQueuedCount;
    }

    public String getMaxQueuedBytes() {
        return maxQueuedBytes;
    }

    public void setMaxQueuedBytes(String maxQueuedBytes) {
        this.maxQueuedBytes = maxQueuedBytes;
    }

    public String getTotalQueuedDuration() {
        return totalQueuedDuration;
    }

    public void setTotalQueuedDuration(String totalQueuedDuration) {
        this.totalQueuedDuration = totalQueuedDuration;
    }

    public String getMaxQueuedDuration() {
        return maxQueuedDuration;
    }

    public void setMaxQueuedDuration(String maxQueuedDuration) {
        this.maxQueuedDuration = maxQueuedDuration;
    }

    public String getFlowFilesIn() {
        return flowFilesIn;
    }

    public void setFlowFilesIn(String flowFilesIn) {
        this.flowFilesIn = flowFilesIn;
    }

    public String getBytesIn() {
        return bytesIn;
    }

    public void setBytesIn(String bytesIn) {
        this.bytesIn = bytesIn;
    }

    public String getFlowFilesOut() {
        return flowFilesOut;
    }

    public void setFlowFilesOut(String flowFilesOut) {
        this.flowFilesOut = flowFilesOut;
    }

    public String getBytesOut() {
        return bytesOut;
    }

    public void setBytesOut(String bytesOut) {
        this.bytesOut = bytesOut;
    }

    public String getPercentUseCount() {
        return percentUseCount;
    }

    public void setPercentUseCount(String percentUseCount) {
        this.percentUseCount = percentUseCount;
    }

    public String getPercentUseBytes() {
        return percentUseBytes;
    }

    public void setPercentUseBytes(String percentUseBytes) {
        this.percentUseBytes = percentUseBytes;
    }
}

package org.apache.nifi.web.api.dto;

import java.util.Date;

/**
 * Stub implementation of ProcessorStatusDTO for compilation purposes.
 * This is a simplified version of the original class from Apache NiFi.
 */
public class ProcessorStatusDTO {
    private String id;
    private String groupId;
    private String name;
    private String type;
    private String runStatus;
    private Date statsLastRefreshed;
    private String inputCount;
    private String inputBytes;
    private String outputCount;
    private String outputBytes;
    private String bytesRead;
    private String bytesWritten;
    private String taskCount;
    private String tasksDurationNanos;
    private String tasksDuration;
    private String activeThreadCount;
    private String terminatedThreadCount;
    private String flowFilesRemoved;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getGroupId() {
        return groupId;
    }

    public void setGroupId(String groupId) {
        this.groupId = groupId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getRunStatus() {
        return runStatus;
    }

    public void setRunStatus(String runStatus) {
        this.runStatus = runStatus;
    }

    public Date getStatsLastRefreshed() {
        return statsLastRefreshed;
    }

    public void setStatsLastRefreshed(Date statsLastRefreshed) {
        this.statsLastRefreshed = statsLastRefreshed;
    }

    public String getInputCount() {
        return inputCount;
    }

    public void setInputCount(String inputCount) {
        this.inputCount = inputCount;
    }

    public String getInputBytes() {
        return inputBytes;
    }

    public void setInputBytes(String inputBytes) {
        this.inputBytes = inputBytes;
    }

    public String getOutputCount() {
        return outputCount;
    }

    public void setOutputCount(String outputCount) {
        this.outputCount = outputCount;
    }

    public String getOutputBytes() {
        return outputBytes;
    }

    public void setOutputBytes(String outputBytes) {
        this.outputBytes = outputBytes;
    }

    public String getBytesRead() {
        return bytesRead;
    }

    public void setBytesRead(String bytesRead) {
        this.bytesRead = bytesRead;
    }

    public String getBytesWritten() {
        return bytesWritten;
    }

    public void setBytesWritten(String bytesWritten) {
        this.bytesWritten = bytesWritten;
    }

    public String getTaskCount() {
        return taskCount;
    }

    public void setTaskCount(String taskCount) {
        this.taskCount = taskCount;
    }

    public String getTasksDurationNanos() {
        return tasksDurationNanos;
    }

    public void setTasksDurationNanos(String tasksDurationNanos) {
        this.tasksDurationNanos = tasksDurationNanos;
    }

    public String getTasksDuration() {
        return tasksDuration;
    }

    public void setTasksDuration(String tasksDuration) {
        this.tasksDuration = tasksDuration;
    }

    public String getActiveThreadCount() {
        return activeThreadCount;
    }

    public void setActiveThreadCount(String activeThreadCount) {
        this.activeThreadCount = activeThreadCount;
    }

    public String getTerminatedThreadCount() {
        return terminatedThreadCount;
    }

    public void setTerminatedThreadCount(String terminatedThreadCount) {
        this.terminatedThreadCount = terminatedThreadCount;
    }

    public String getFlowFilesRemoved() {
        return flowFilesRemoved;
    }

    public void setFlowFilesRemoved(String flowFilesRemoved) {
        this.flowFilesRemoved = flowFilesRemoved;
    }
}

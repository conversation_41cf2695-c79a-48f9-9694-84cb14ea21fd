package com.siteweb.tcs.graph.service;

import com.siteweb.tcs.graph.model.node.DeviceNode;
import com.siteweb.tcs.graph.model.node.GatewayNode;
import com.siteweb.tcs.graph.model.node.RegionNode;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Service interface for querying the graph database
 */
public interface GraphQueryService {
    
    /**
     * Get the region hierarchy
     * 
     * @return the complete region hierarchy
     */
    List<RegionNode> getRegionHierarchy();
    
    /**
     * Get a region with its descendants
     * 
     * @param regionId the ID of the region
     * @param depth the maximum depth to traverse
     * @return the region with its descendants
     */
    Optional<RegionNode> getRegionWithDescendants(Long regionId, int depth);
    
    /**
     * Get all gateways in a region
     * 
     * @param regionId the ID of the region
     * @param depth the maximum depth to traverse
     * @return list of gateways in the region
     */
    List<GatewayNode> getGatewaysInRegion(Long regionId, int depth);
    
    /**
     * Get all devices in a region
     * 
     * @param regionId the ID of the region
     * @param depth the maximum depth to traverse
     * @return list of devices in the region
     */
    List<DeviceNode> getDevicesInRegion(Long regionId, int depth);
    
    /**
     * Get devices by type in a region
     * 
     * @param regionId the ID of the region
     * @param deviceType the device type
     * @param depth the maximum depth to traverse
     * @return list of devices of the given type in the region
     */
    List<DeviceNode> getDevicesByTypeInRegion(Long regionId, String deviceType, int depth);
    
    /**
     * Get devices by manufacturer in a region
     * 
     * @param regionId the ID of the region
     * @param manufacturer the manufacturer name
     * @param depth the maximum depth to traverse
     * @return list of devices from the given manufacturer in the region
     */
    List<DeviceNode> getDevicesByManufacturerInRegion(Long regionId, String manufacturer, int depth);
    
    /**
     * Get a gateway with its devices
     * 
     * @param gatewayId the ID of the gateway
     * @return the gateway with its devices
     */
    Optional<GatewayNode> getGatewayWithDevices(String gatewayId);
    
    /**
     * Find the path between two entities
     * 
     * @param sourceId the ID of the source entity
     * @param targetId the ID of the target entity
     * @return the path between the entities
     */
    List<Map<String, Object>> findPath(String sourceId, String targetId);
    
    /**
     * Get statistics about the graph
     * 
     * @return statistics about the graph
     */
    Map<String, Object> getGraphStatistics();
}

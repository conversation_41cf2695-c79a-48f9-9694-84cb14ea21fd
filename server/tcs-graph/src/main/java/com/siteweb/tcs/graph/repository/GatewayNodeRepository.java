package com.siteweb.tcs.graph.repository;

import com.siteweb.tcs.graph.model.node.GatewayNode;
import org.springframework.data.neo4j.repository.Neo4jRepository;
import org.springframework.data.neo4j.repository.query.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Repository for GatewayNode entities
 */
@Repository
public interface GatewayNodeRepository extends Neo4jRepository<GatewayNode, Long> {
    
    /**
     * Find a gateway by its source ID
     * 
     * @param sourceId the source ID from the relational database
     * @return the gateway node if found
     */
    Optional<GatewayNode> findBySourceId(String sourceId);
    
    /**
     * Find gateways by plugin ID
     * 
     * @param pluginId the plugin ID
     * @return list of gateways for the given plugin
     */
    List<GatewayNode> findByPluginId(String pluginId);
    
    /**
     * Find gateways by manufacturer
     * 
     * @param manufacturer the manufacturer name
     * @return list of gateways from the given manufacturer
     */
    List<GatewayNode> findBy<PERSON>anufacturer(String manufacturer);
    
    /**
     * Find gateways by model
     * 
     * @param model the model name
     * @return list of gateways of the given model
     */
    List<GatewayNode> findByModel(String model);
    
    /**
     * Find a gateway with all its devices
     * 
     * @param sourceId the source ID of the gateway
     * @return the gateway with all its devices
     */
    @Query("MATCH (g:Gateway {source_id: $sourceId}) " +
           "OPTIONAL MATCH (g)-[:HAS_DEVICE]->(d:Device) " +
           "RETURN g, collect(d) as devices")
    Optional<GatewayNode> findGatewayWithDevices(@Param("sourceId") String sourceId);
    
    /**
     * Find all gateways in a region (directly or indirectly)
     * 
     * @param regionSourceId the source ID of the region
     * @param depth the maximum depth to traverse
     * @return list of gateways in the region
     */
    @Query("MATCH (r:Region {source_id: $regionSourceId})-[:CONTAINS*1..$depth]->(g:Gateway) " +
           "RETURN g")
    List<GatewayNode> findGatewaysInRegion(@Param("regionSourceId") String regionSourceId, @Param("depth") int depth);
}

package com.siteweb.tcs.common.expression;


import com.siteweb.tcs.common.expression.enums.ValueScope;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 值表达式
 * <AUTHOR> (2025-02-18)
 **/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ValueExpression {
    /**
     * 属性范围、性质
     */
    private ValueScope scope;

    /**
     * 属性名/表达式/值等
     */
    private String text;

    public String toExpression() {
        return scope.buildMethod.apply(text);
    }





}

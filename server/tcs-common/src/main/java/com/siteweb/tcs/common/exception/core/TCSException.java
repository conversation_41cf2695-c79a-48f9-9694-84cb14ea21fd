package com.siteweb.tcs.common.exception.core;

import com.siteweb.tcs.common.exception.code.ErrorCode;
import lombok.Getter;

import java.io.Serial;

/**
 * Base exception class for all TCS exceptions.
 * All custom exceptions in the system should extend this class or its subclasses.
 */
@Getter
public class TCSException extends RuntimeException {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Error code
     */
    private final String code;

    /**
     * Error message
     */
    private final String message;

    /**
     * Error details
     */
    private final Object details;

    /**
     * Source module or component that generated the exception
     */
    private final String source;

    public TCSException(String code, String message) {
        this(code, message, null, null);
    }

    public TCSException(String code, String message, Throwable cause) {
        this(code, message, null, cause);
    }

    public TCSException(String code, String message, Object details) {
        this(code, message, details, null);
    }

    public TCSException(String code, String message, Object details, Throwable cause) {
        this(code, message, details, cause, null);
    }

    public TCSException(String code, String message, Object details, Throwable cause, String source) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.details = details;
        this.source = source;
    }

    public TCSException(ErrorCode errorCode) {
        this(errorCode.getCode(), errorCode.getMessage());
    }

    public TCSException(ErrorCode errorCode, Object details) {
        this(errorCode.getCode(), errorCode.getMessage(), details);
    }

    public TCSException(ErrorCode errorCode, Throwable cause) {
        this(errorCode.getCode(), errorCode.getMessage(), null, cause);
    }

    public TCSException(ErrorCode errorCode, Object details, Throwable cause) {
        this(errorCode.getCode(), errorCode.getMessage(), details, cause);
    }

    public TCSException(ErrorCode errorCode, Object details, Throwable cause, String source) {
        this(errorCode.getCode(), errorCode.getMessage(), details, cause, source);
    }
}

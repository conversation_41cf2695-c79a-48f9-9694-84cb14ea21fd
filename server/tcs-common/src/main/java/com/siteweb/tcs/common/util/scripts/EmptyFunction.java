package com.siteweb.tcs.common.util.scripts;

import com.googlecode.aviator.runtime.function.AbstractFunction;
import com.googlecode.aviator.runtime.type.AviatorBoolean;
import com.googlecode.aviator.runtime.type.AviatorObject;

import java.util.Collection;
import java.util.Map;

/**
 * Aviator 脚本的 empty方法实现，用于获取 数组、列表、Map是否为空
 * 当参数为null时也认为为空
 *
 * <AUTHOR> (2025-02-20)
 **/
public class EmptyFunction extends AbstractFunction {
    @Override
    public String getName() {
        return "EMPTY$";
    }


    @Override
    public AviatorObject call(Map<String, Object> env, AviatorObject arg1) {
        var param = arg1.getValue(env);
        if (param == null) return AviatorBoolean.valueOf(true);
        if (param instanceof String str) {
            return AviatorBoolean.valueOf(str.isEmpty());
        }
        if (param instanceof Object[] array) {
            return AviatorBoolean.valueOf(array.length == 0);
        }
        if (param instanceof Collection<?> list) {
            return AviatorBoolean.valueOf(list.isEmpty());
        }
        if (param instanceof Map<?, ?> map) {
            return AviatorBoolean.valueOf(map.isEmpty());
        }
        return AviatorBoolean.valueOf(false);
    }
}

package com.siteweb.tcs.common.config;

import org.flywaydb.core.Flyway;
import org.flywaydb.core.api.MigrationInfo;
import org.flywaydb.core.api.MigrationInfoService;
import org.flywaydb.core.api.callback.Callback;
import org.flywaydb.core.api.callback.Context;
import org.flywaydb.core.api.callback.Event;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.io.ResourceLoader;

import javax.sql.DataSource;
import java.util.Arrays;

/**
 * Flyway数据库迁移配置
 * 替代原有的ScriptManager实现，使用Flyway进行数据库版本控制和脚本管理
 */
@Configuration
public class FlywayConfig {

    private static final Logger logger = LoggerFactory.getLogger(FlywayConfig.class);

    @Autowired
    private DataSource dataSource;

    @Autowired
    private ResourceLoader resourceLoader;

    @Value("${spring.profiles.active:mysql}")
    private String activeProfile;

    @Value("${tcs.script.auto-execute:true}")
    private boolean autoExecuteScripts;

    @Value("${tcs.script.force-update:false}")
    private boolean forceUpdate;

    @Value("${tcs.script.base-path:classpath:sql/}")
    private String scriptBasePath;

    @Value("${tcs.script.safe-mode:true}")
    private boolean safeMode;

    @Value("${tcs.script.backup-before-update:true}")
    private boolean backupBeforeUpdate;

//    @Value("${flyway.locations:classpath:db/core/migrations,classpath:db/core/migrations/${spring.profiles.active:mysql}}")
//    private String[] locations;

    @Value("${classpath:db/core/migrations/${spring.profiles.active:h2}}")
    private String[] locations;

    @Value("${flyway.table:tcs_flyway_schema_history}")
    private String table;

    @Value("${flyway.baseline-on-migrate:true}")
    private boolean baselineOnMigrate;

    @Value("${flyway.validate-on-migrate:true}")
    private boolean validateOnMigrate;

    @Value("${flyway.out-of-order:false}")
    private boolean outOfOrder;

    @Value("${flyway.placeholders.dbType:${spring.profiles.active:mysql}}")
    private String dbType;

    /**
     * 配置Flyway实例
     *
     * @return Flyway实例
     */
    @Bean
    @Primary
    public Flyway flyway() {
        logger.info("初始化Flyway数据库迁移工具");
        logger.info("数据库类型: {}", activeProfile);
        logger.info("脚本位置: {}", Arrays.toString(locations));

        // 创建Flyway配置
        org.flywaydb.core.api.configuration.FluentConfiguration configuration = Flyway.configure()
                .dataSource(dataSource)
                .locations(locations)
                .baselineOnMigrate(baselineOnMigrate)
                .validateOnMigrate(validateOnMigrate)
                .outOfOrder(outOfOrder)
                .table(table)
                .placeholders(java.util.Collections.singletonMap("dbType", dbType))
                .callbacks(new FlywayCallback());

        // 创建Flyway实例
        Flyway flyway = new Flyway(configuration);
        flyway.repair();
        // 如果启用了自动执行脚本，则执行迁移
        if (autoExecuteScripts) {
            if (forceUpdate) {
                logger.info("强制更新模式已启用，将重新执行所有脚本");
                flyway.clean();
            }

            // 执行迁移
            flyway.migrate();

            // 输出迁移信息
            MigrationInfoService migrationInfoService = flyway.info();
            MigrationInfo[] migrations = migrationInfoService.all();
            logger.info("已执行 {} 个迁移脚本", migrations.length);
            for (MigrationInfo migration : migrations) {
                logger.info("迁移脚本: {}，版本: {}，描述: {}，状态: {}",
                        migration.getScript(),
                        migration.getVersion(),
                        migration.getDescription(),
                        migration.getState());
            }
        } else {
            logger.info("自动执行脚本已禁用，跳过迁移");
        }

        return flyway;
    }

    /**
     * Flyway回调类，用于在迁移前后执行自定义逻辑
     */
    private static class FlywayCallback implements Callback {

        private static final Logger callbackLogger = LoggerFactory.getLogger(FlywayCallback.class);

        @Override
        public boolean supports(Event event, Context context) {
            return event == Event.BEFORE_MIGRATE ||
                   event == Event.AFTER_MIGRATE ||
                   event == Event.BEFORE_EACH_MIGRATE ||
                   event == Event.AFTER_EACH_MIGRATE;
        }

        @Override
        public boolean canHandleInTransaction(Event event, Context context) {
            return true;
        }

        @Override
        public void handle(Event event, Context context) {
            if (event == Event.BEFORE_MIGRATE) {
                callbackLogger.info("开始执行数据库迁移");
            } else if (event == Event.AFTER_MIGRATE) {
                callbackLogger.info("数据库迁移执行完成");
            } else if (event == Event.BEFORE_EACH_MIGRATE) {
                callbackLogger.info("开始执行迁移脚本: {}", context.getMigrationInfo().getScript());
            } else if (event == Event.AFTER_EACH_MIGRATE) {
                callbackLogger.info("迁移脚本执行完成: {}", context.getMigrationInfo().getScript());
            }
        }

        @Override
        public String getCallbackName() {
            return FlywayCallback.class.getSimpleName();
        }
    }
}
package com.siteweb.tcs.common.o11y;

import com.google.common.util.concurrent.AtomicDouble;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tags;
import org.springframework.data.util.Pair;

import java.util.HashMap;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledFuture;
import java.util.concurrent.atomic.AtomicLong;

public class MetricInstrument {
    //create variable hashmap for window log
    private final HashMap<String, WindowLogQueue> windowLogs = new HashMap<String, WindowLogQueue>();
    private final HashMap<String, Counter> counters = new HashMap<String, Counter>();
    private final HashMap<String, AtomicDouble> gauges = new HashMap<String, AtomicDouble>();

    private final ConcurrentHashMap<String, RateCalculator> rateCalculators = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> rateSources = new ConcurrentHashMap<>();

    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);
    private final ConcurrentHashMap<String, ScheduledFuture<?>> scheduledTasks = new ConcurrentHashMap<>();
    private boolean enableWindowLog = false;
    private final MeterRegistry registry = Metrics.globalRegistry;
    private String o11lyMetricNamespace;
    private String tag;

    public MetricInstrument() {
    }

    public MetricInstrument(Class<?> cls) {
        setNamespace(cls);
    }

    public void setEnableWindowLog(boolean enableWindowLog) {
        this.enableWindowLog = enableWindowLog;
    }

    public boolean getEnableWindowLog() {
        return enableWindowLog;
    }

    public void addWindowLog(String name, int capacity) {
        if (!windowLogs.containsKey(name)) {
            windowLogs.put(name, new WindowLogQueue(capacity));
        }
    }

    public void addWindowLog(String name) {
        if (!windowLogs.containsKey(name)) {
            windowLogs.put(name, new WindowLogQueue(100));
        }
    }

    public void addCounter(String name) {
        counters.put(name, registry.counter(name, Tags.of(o11lyMetricNamespace, tag)));
    }

    public void incrementCounter(String name) {
        counters.get(name).increment();
    }

    public void incrementCounterAmount(String name, Integer amount) {
        counters.get(name).increment(amount);
    }

    public void addGauge(String name) {
        var gauge = registry.gauge(name, Tags.of(o11lyMetricNamespace, tag), new AtomicDouble(0));
        gauges.put(name, gauge);
    }

    public void updateGauge(String name, long value) {
        gauges.get(name).set(value);
    }

    public void enqueueWindowLogItem(String name, WindowLogItem item) {
        if (!windowLogs.containsKey(name)) {
            windowLogs.put(name, new WindowLogQueue(100));
        }

        if (enableWindowLog) {
            windowLogs.get(name).enqueue(item);
        }
    }

    public void setNamespace(Class<?> cls) {
        this.o11lyMetricNamespace = cls.getPackage().getName();
        this.tag = cls.getSimpleName();
    }

    public WindowLogQueue getWindowLogQueue(String queueName) {
        return windowLogs.get(queueName);
    }

    public Long getCounterAmount(String name) {
        return (long) counters.get(name).count();
    }

    public List<String> getLogQueueNames() {
        return windowLogs.keySet().stream().toList();
    }

    public void resetCounter(String name) {
        // Remove the existing counter from the registry
        Counter counterToRemove = counters.remove(name);
        if (counterToRemove != null) {
            Metrics.globalRegistry.remove(counterToRemove);
        }

        // Recreate the counter with the same name and tags
        counters.put(name, registry.counter(name, Tags.of(o11lyMetricNamespace, tag)));
    }

    public List<Pair<String, Double>> getGaugeList() {
        return gauges.entrySet().stream()
                .map(entry -> Pair.of(entry.getKey(), entry.getValue().get()))
                .toList();
    }

    public List<Pair<String, Long>> getCounterList() {
        return counters.entrySet().stream()
                .map(entry -> Pair.of(entry.getKey(), (long) entry.getValue().count()))
                .toList();
    }

    public List<Pair<String, Long>> getQueueSizeList() {
        return windowLogs.entrySet().stream()
                .map(entry -> Pair.of(entry.getKey(), entry.getValue().getSize()))
                .toList();
    }

    public Double getGaugeValue(String name) {
        return gauges.get(name).get();
    }


    // 创建一个速率计算器
    public void addRateCalculator(String name, int windowSizeInSeconds) {
        RateCalculator calculator = new RateCalculator(windowSizeInSeconds);
        rateCalculators.put(name, calculator);
    }

    // 停止速率计算器
    public void stopRateCalculator(String name) {
        rateCalculators.remove(name);
    }

    // 更新速率计算器的值
    public void updateRateSource(String name, long value) {
        RateCalculator calculator = rateCalculators.get(name);
        if (calculator != null) {
            System.out.println("MetricInstrument.updateRateSource-------------------------" + name + value);
            calculator.updateValue(value);
        }
    }

    // 增加速率计算器的值
    public void incrementRateSource(String name) {
        RateCalculator calculator = rateCalculators.get(name);
        if (calculator != null) {
            calculator.incrementValue();
        }
    }

    // 获取速率计算器的累积值
    public long getRateValue(String name) {
        RateCalculator calculator = rateCalculators.get(name);
        return calculator != null ? calculator.getRate() : 0;
    }

    public void clearRateCalculator() {
        rateCalculators.clear();
    }

}

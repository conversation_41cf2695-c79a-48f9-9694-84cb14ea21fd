package com.siteweb.tcs.common.exception.code;

/**
 * Standard technical error codes for the system.
 * These are common technical error codes that can be used across the system.
 */
public enum StandardTechnicalErrorCode implements TechnicalErrorCode {
    // System errors
    SYSTEM_ERROR("SYSTEM_ERROR", "系统内部错误"),
    SERVICE_UNAVAILABLE("SERVICE_UNAVAILABLE", "服务不可用"),
    TIMEOUT("TIMEOUT", "操作超时"),
    RESOURCE_EXHAUSTED("RESOURCE_EXHAUSTED", "资源耗尽"),
    
    // Network errors
    NETWORK_ERROR("NETWORK_ERROR", "网络错误"),
    CONNECTION_REFUSED("CONNECTION_REFUSED", "连接被拒绝"),
    CONNECTION_TIMEOUT("CONNECTION_TIMEOUT", "连接超时"),
    CONNECTION_RESET("CONNECTION_RESET", "连接重置"),
    
    // Database errors
    DATABASE_ERROR("DATABASE_ERROR", "数据库错误"),
    DATABASE_CONNECTION_ERROR("DATABASE_CONNECTION_ERROR", "数据库连接错误"),
    DATABASE_QUERY_ERROR("DATABASE_QUERY_ERROR", "数据库查询错误"),
    DATABASE_UPDATE_ERROR("DATABASE_UPDATE_ERROR", "数据库更新错误"),
    
    // File system errors
    FILE_SYSTEM_ERROR("FILE_SYSTEM_ERROR", "文件系统错误"),
    FILE_NOT_FOUND("FILE_NOT_FOUND", "文件不存在"),
    FILE_PERMISSION_DENIED("FILE_PERMISSION_DENIED", "文件权限被拒绝"),
    FILE_IO_ERROR("FILE_IO_ERROR", "文件IO错误"),
    
    // Configuration errors
    CONFIGURATION_ERROR("CONFIGURATION_ERROR", "配置错误"),
    INVALID_CONFIGURATION("INVALID_CONFIGURATION", "无效的配置"),
    MISSING_CONFIGURATION("MISSING_CONFIGURATION", "缺少配置"),
    
    // API errors
    API_ERROR("API_ERROR", "API错误"),
    API_NOT_FOUND("API_NOT_FOUND", "API不存在"),
    API_METHOD_NOT_ALLOWED("API_METHOD_NOT_ALLOWED", "API方法不允许"),
    API_UNSUPPORTED_MEDIA_TYPE("API_UNSUPPORTED_MEDIA_TYPE", "API不支持的媒体类型"),
    
    // Serialization errors
    SERIALIZATION_ERROR("SERIALIZATION_ERROR", "序列化错误"),
    DESERIALIZATION_ERROR("DESERIALIZATION_ERROR", "反序列化错误"),
    
    // Security errors
    SECURITY_ERROR("SECURITY_ERROR", "安全错误"),
    ENCRYPTION_ERROR("ENCRYPTION_ERROR", "加密错误"),
    DECRYPTION_ERROR("DECRYPTION_ERROR", "解密错误"),
    
    // Validation errors
    VALIDATION_ERROR("VALIDATION_ERROR", "验证错误"),
    INVALID_PARAMETER("INVALID_PARAMETER", "无效的参数"),
    MISSING_PARAMETER("MISSING_PARAMETER", "缺少参数"),
    
    // Concurrency errors
    CONCURRENCY_ERROR("CONCURRENCY_ERROR", "并发错误"),
    DEADLOCK("DEADLOCK", "死锁"),
    RACE_CONDITION("RACE_CONDITION", "竞态条件");

    private final String code;
    private final String message;

    StandardTechnicalErrorCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Override
    public String getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}

package com.siteweb.tcs.common.exception.core;

import com.siteweb.tcs.common.exception.code.ErrorCode;
import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;
import lombok.Getter;

import java.io.Serial;

/**
 * Technical exception class for system and infrastructure related exceptions.
 * This exception type should be used for all technical/system errors.
 */
@Getter
public class TechnicalException extends TCSException {
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Technical component that generated the exception
     */
    private final String component;

    public TechnicalException(String code, String message) {
        this(code, message, null, null, null);
    }

    public TechnicalException(String code, String message, String component) {
        this(code, message, null, null, component);
    }

    public TechnicalException(String code, String message, Throwable cause) {
        this(code, message, null, cause, null);
    }

    public TechnicalException(String code, String message, Object details) {
        this(code, message, details, null, null);
    }

    public TechnicalException(String code, String message, Object details, Throwable cause) {
        this(code, message, details, cause, null);
    }

    public TechnicalException(String code, String message, Object details, Throwable cause, String component) {
        super(code, message, details, cause);
        this.component = component;
    }

    public TechnicalException(TechnicalErrorCode errorCode) {
        super(errorCode);
        this.component = null;
    }

    public TechnicalException(TechnicalErrorCode errorCode, String component) {
        super(errorCode);
        this.component = component;
    }

    public TechnicalException(TechnicalErrorCode errorCode, Object details) {
        super(errorCode, details);
        this.component = null;
    }

    public TechnicalException(TechnicalErrorCode errorCode, Throwable cause) {
        super(errorCode, cause);
        this.component = null;
    }

    public TechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause) {
        super(errorCode, details, cause);
        this.component = null;
    }

    public TechnicalException(TechnicalErrorCode errorCode, Object details, Throwable cause, String component) {
        super(errorCode, details, cause);
        this.component = component;
    }
}

package com.siteweb.stream.core.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <AUTHOR> (2025-05-28)
 **/
@Data
@TableName("tcs_stream_modules")
public class StreamModule {

    // 模块唯一ID
    @TableId(value = "moduleId", type = IdType.AUTO)
    private String moduleId;

    // 模块名称
    private String moduleName;

    // 模块版本
    private String moduleVersion;

    // 模块支持
    private String moduleProvider;

    // Jar包路径
    private String jarFile;

    // Jar包code
    private String jarCode;


    // 是否启用
    private Boolean enable;

    // 模块支持
    private LocalDateTime createTime;

    // 模块支持
    private LocalDateTime updateTime;

}

package com.siteweb.stream.defaults.basics;

import com.siteweb.stream.common.annotations.*;
import com.siteweb.stream.common.messages.StateEventMessage;
import com.siteweb.stream.common.messages.StreamMessage;
import com.siteweb.stream.defaults.messages.TickMessage;
import com.siteweb.stream.common.runtime.AbstractShape;
import com.siteweb.stream.common.stream.ShapeRuntimeContext;
import com.siteweb.stream.common.stream.StreamShapeOption;
import com.siteweb.stream.defaults.options.TimerShapeOption;
import com.siteweb.stream.defaults.options.defaults.TimerDefaultOption;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.Cancellable;

import java.time.Duration;
import java.time.LocalDateTime;

/**
 * 固定间隔定时器
 *
 * @ClassName: TimerShape
 * @descriptions:
 * @author: xsx
 * @date: 2/17/2025 9:12 AM
 **/
@Slf4j
@EditorHidden
@Shape(type = "fixed-timer")
@ShapeVersion(major = 1, minor = 0, patch = 0)
@ShapeIcon("icon-font")
@ShapeAuthor("Vertiv")
@ShapeDefaultOptions(TimerDefaultOption.class)
@ShapeOutlet(id = 0x01, type = TickMessage.class, desc = "滴答事件")
@ShapeOutlet(id = 0x02, type = StateEventMessage.class, desc = "启动事件")
@ShapeOutlet(id = 0x03, type = StateEventMessage.class, desc = "停止事件")
public class TimerShape extends AbstractShape {


    private Cancellable scheduler;


    @Recoverable
    private TimerShapeOption options;

    /**
     * 定时器共触发了多少次 调试用
     */
    @Recoverable
    @Traceable
    private Integer tickCount = 0;


    public TimerShape(ShapeRuntimeContext context) {
        super(context);
    }

    @Override
    protected void onOptionReset(StreamShapeOption options) {
        if (options instanceof TimerShapeOption timerShapeOptions) {
            this.options = timerShapeOptions;
            // 配置重置后 恢复计时器状态
            if (isStarted()) onStart();
        }
    }

    @Override
    protected void processMessage(StreamMessage in) {


    }


    @Override
    protected void onRestart(Throwable reason) {
        // 重启后 恢复计时器状态
        if (isStarted()) onStart();
    }


    @Override
    protected void onStart() {
        destroyTimer();
        if (options.isDefaultState()) {
            createTimer();
        }
    }

    @Override
    protected void onStop() {
        if (scheduler != null) destroyTimer();
    }


    /**
     * 创建计时器，如果计时器已存在则先销毁计时器
     */
    private void createTimer() {
        if (options == null) return;
        tickCount = 0;
        scheduler = getContext().getSystem().scheduler().scheduleAtFixedRate(
                Duration.ofSeconds(options.getFirstDelay()),          // 初始延迟
                Duration.ofSeconds(options.getInterval()),            // 间隔时间
                this::onTimer,                  // 触发回调
                getContext().getDispatcher());  // 调度器
        // 输出启动事件
        var msg = new StateEventMessage();
        msg.setPayload(true);
        msg.setTimestamp(LocalDateTime.now());
        context.getOutLet((short) 0x02).broadcast(msg);
    }


    /**
     * 销毁计时器
     */
    private void destroyTimer() {
        if (scheduler != null) {
            scheduler.cancel();
            scheduler = null;
            // 输出停止事件
            var msg = new StateEventMessage();
            msg.setPayload(false);
            msg.setTimestamp(LocalDateTime.now());
            context.getOutLet((short) 0x03).broadcast(msg);
        }
    }

    /**
     * tick事件，输出至0x01输出口
     */
    private void onTimer() {
        tickCount++;
        var msg = new TickMessage();
        msg.setTickCount(tickCount);
        msg.setTimestamp(LocalDateTime.now());
        context.getOutLet((short) 0x01).broadcast(msg);
    }

}

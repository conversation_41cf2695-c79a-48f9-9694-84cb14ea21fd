package com.siteweb.stream.defaults.messages;

import com.siteweb.stream.common.annotations.StreamDataType;
import com.siteweb.stream.common.messages.TextMessage;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.pekko.http.javadsl.model.ContentType;
import org.apache.pekko.http.javadsl.model.ContentTypes;
import org.apache.pekko.http.javadsl.model.HttpMethod;
import org.apache.pekko.http.javadsl.model.HttpMethods;


/**
 * Http请求，UTF8编码
 * <AUTHOR> (2025-04-24)
 **/
@Data
@StreamDataType
@EqualsAndHashCode(callSuper = true)
public class HTTPRequestMessage extends TextMessage {
    private HttpMethod method = HttpMethods.GET;
    private ContentType.NonBinary contentType = ContentTypes.APPLICATION_JSON;
}

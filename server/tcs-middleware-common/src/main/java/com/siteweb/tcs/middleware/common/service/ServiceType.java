package com.siteweb.tcs.middleware.common.service;

/**
 * 服务类型枚举
 * 用于标识不同类型的服务
 */
public enum ServiceType {
    /**
     * 数据库服务
     */
    DATABASE("DATABASE", "RELATIONAL_DB"),

    /**
     * 键值存储服务
     */
    KEY_VALUE_STORE("KEY_VALUE_STORE", "KEY_VALUE_STORE"),

    /**
     * 消息队列服务
     */
    MESSAGE_QUEUE("MESSAGE_QUEUE", "MESSAGE_QUEUE"),

    /**
     * 时序数据库服务
     */
    TIME_SERIES_DB("TIME_SERIES_DB", "TIME_SERIES_DB"),

    /**
     * 文件存储服务
     */
    FILE_STORAGE("FILE_STORAGE", "FILE_STORAGE"),

    /**
     * Web服务
     */
    WEB_SERVICE("WEB_SERVICE", "WEB_SERVER"),

    /**
     * Siteweb持久化服务
     * 用于封装对tcs-siteweb模块的集成，支持关系型数据库
     */
    SITEWEB_PERSISTENT("SITEWEB_PERSISTENT", "RELATIONAL_DB"),

    /**
     * 无类型
     * 用于注解的默认值
     */
    NONE("NONE", "NONE");

    private final String code;
    private final String supportedResourceCategory;

    ServiceType(String code, String supportedResourceCategory) {
        this.code = code;
        this.supportedResourceCategory = supportedResourceCategory;
    }

    /**
     * 获取服务类型编码
     *
     * @return 服务类型编码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取支持的资源类别
     *
     * @return 支持的资源类别
     */
    public String getSupportedResourceCategory() {
        return supportedResourceCategory;
    }

    /**
     * 根据编码获取服务类型
     *
     * @param code 服务类型编码
     * @return 服务类型，如果不存在则返回null
     */
    public static ServiceType fromCode(String code) {
        if (code == null) {
            return null;
        }

        for (ServiceType type : values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 根据编码获取服务类型（忽略大小写）
     *
     * @param code 服务类型编码
     * @return 服务类型，如果不存在则返回null
     */
    public static ServiceType fromCodeIgnoreCase(String code) {
        if (code == null) {
            return null;
        }

        for (ServiceType type : values()) {
            if (type.getCode().equalsIgnoreCase(code)) {
                return type;
            }
        }
        return null;
    }

    /**
     * 判断服务类型是否支持指定的资源类别
     *
     * @param resourceCategory 资源类别
     * @return 是否支持
     */
    public boolean isSupportedResourceCategory(String resourceCategory) {
        return this.supportedResourceCategory.equals(resourceCategory);
    }
}

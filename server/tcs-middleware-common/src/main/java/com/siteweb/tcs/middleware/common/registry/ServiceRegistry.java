package com.siteweb.tcs.middleware.common.registry;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.lifecycle.IServiceInitializer;
import com.siteweb.tcs.middleware.common.service.Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.stream.Collectors;

/**
 * 服务注册表
 * 用于管理服务实例
 */
@Component
public class ServiceRegistry {

    private static final Logger logger = LoggerFactory.getLogger(ServiceRegistry.class);

    private final ConcurrentMap<String, Service> services = new ConcurrentHashMap<>();

    private IServiceInitializer serviceInitializer;
    private ApplicationContext applicationContext;

    @Autowired
    public void setServiceInitializer(IServiceInitializer serviceInitializer) {
        this.serviceInitializer = serviceInitializer;
    }

    @Autowired
    public void setApplicationContext(ApplicationContext applicationContext) {
        this.applicationContext = applicationContext;
    }

    /**
     * 保存服务到注册表
     *
     * @param service 服务实例
     * @throws MiddlewareBusinessException 如果保存失败
     */
    public void save(Service service) throws MiddlewareBusinessException {
        if (service == null) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Service cannot be null"
            );
        }
        services.put(service.getId(), service);
        logger.debug("Service saved to registry: {}", service.getId());
    }

    /**
     * 根据ID获取服务
     * 如果服务不存在，可能会尝试创建
     *
     * @param serviceId 服务ID
     * @return 服务实例，如果不存在则返回null
     */
    public Service get(String serviceId) {
        Service service = services.get(serviceId);

        // 如果服务不存在，尝试从服务初始化器获取
        if (service == null && serviceInitializer != null) {
            try {
                logger.debug("Service not found in registry, trying to initialize: {}", serviceId);
                service = serviceInitializer.initializeServiceById(serviceId);
            } catch (Exception e) {
                logger.warn("Failed to initialize service: {}", serviceId, e);
            }
        }

        return service;
    }

    /**
     * 获取所有服务
     *
     * @return 所有服务的集合
     */
    public Collection<Service> getAll() {
        return Collections.unmodifiableCollection(services.values());
    }

    /**
     * 根据类型获取服务
     *
     * @param type 服务类型
     * @return 指定类型的服务集合
     */
    public Collection<Service> getByType(String type) {
        return services.values().stream()
                .filter(service -> service.getType().equals(type))
                .collect(Collectors.toList());
    }

    /**
     * 从注册表中移除服务
     *
     * @param serviceId 服务ID
     * @return 被移除的服务，如果不存在则返回null
     */
    public Service remove(String serviceId) {
        logger.debug("Removing service from registry: {}", serviceId);
        return services.remove(serviceId);
    }

    /**
     * 检查服务是否存在于注册表中
     * 与get方法不同，此方法不会尝试创建不存在的服务
     *
     * @param serviceId 服务ID
     * @return 如果服务存在则返回true，否则返回false
     */
    public boolean contains(String serviceId) {
        return services.containsKey(serviceId);
    }


}

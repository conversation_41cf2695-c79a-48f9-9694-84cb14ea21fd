package com.siteweb.tcs.middleware.common.model.config;

import lombok.Data;

/**
 * Redis配置类
 */
@Data
public class RedisConfig {
    /**
     * 主机地址
     */
    private String host;

    /**
     * 端口
     */
    private int port = 6379;

    /**
     * 密码
     */
    private String password;

    /**
     * 数据库索引
     */
    private int database = 0;

    /**
     * 连接超时时间（毫秒）
     */
    private int connectionTimeout = 2000;

    /**
     * 命令超时时间（毫秒）
     */
    private int commandTimeout = 2000;

    /**
     * 最大连接数
     */
    private int maxTotal = 8;

    /**
     * 最大空闲连接数
     */
    private int maxIdle = 8;

    /**
     * 最小空闲连接数
     */
    private int minIdle = 0;
}

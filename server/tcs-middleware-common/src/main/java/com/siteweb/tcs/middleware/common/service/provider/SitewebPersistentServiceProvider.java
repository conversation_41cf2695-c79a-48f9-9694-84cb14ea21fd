package com.siteweb.tcs.middleware.common.service.provider;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Siteweb持久化服务提供者
 * 用于创建和管理SitewebPersistentService实例
 */
@Component
public class SitewebPersistentServiceProvider implements ServiceProvider<Service> {

    private static final Logger logger = LoggerFactory.getLogger(SitewebPersistentServiceProvider.class);

    @Autowired
    private ApplicationContext applicationContext;

    @Override
    public String getType() {
        return ServiceType.SITEWEB_PERSISTENT.getCode();
    }

    @Override
    public String getSupportedResourceCategory() {
        return ServiceType.SITEWEB_PERSISTENT.getSupportedResourceCategory();
    }

    @Override
    public ValidationResult validateConfig(Map<String, Object> config) {
        List<String> errors = new ArrayList<>();

        // SitewebPersistentService不需要特殊配置，所以验证总是通过
        if (config == null) {
            // 允许config为null，因为不需要配置
            logger.debug("SitewebPersistentService config is null, which is acceptable");
        }

        return errors.isEmpty() ? ValidationResult.valid() : ValidationResult.invalid(errors);
    }

    @Override
    public ConnectionTestResult testConnection(Map<String, Object> config) {
        try {
            // SitewebPersistentService不需要连接测试，因为它依赖Spring容器
            // 这里只是简单返回成功
            logger.debug("Testing connection for SitewebPersistentService (no actual connection needed)");
            return ConnectionTestResult.success("SitewebPersistentService connection test passed");
        } catch (Exception e) {
            logger.error("Connection test failed for SitewebPersistentService", e);
            return ConnectionTestResult.failure("Connection test failed: " + e.getMessage());
        }
    }

    @Override
    public Service createService(String id, String name, String description, Map<String, Object> config, Resource resource) throws MiddlewareTechnicalException {
        try {
            logger.info("Creating SitewebPersistentService: id={}, name={}, resource={}", id, name, resource != null ? resource.getId() : "null");

            // 验证配置
            ValidationResult validationResult = validateConfig(config);
            if (!validationResult.isValid()) {
                throw new MiddlewareTechnicalException(
                    com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "配置验证失败: " + String.join(", ", validationResult.getErrors())
                );
            }

            // 验证Resource是否提供
            if (resource == null) {
                throw new MiddlewareTechnicalException(
                    com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "SitewebPersistentService requires a database resource: " + id
                );
            }

            // 验证Resource类型
            String resourceType = resource.getType();
            if (!isSupportedResourceType(resourceType)) {
                throw new MiddlewareTechnicalException(
                    com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Unsupported resource type for SitewebPersistentService: " + resourceType + ", service: " + id
                );
            }

            // 创建服务实例，传入Resource
            SitewebPersistentService service = new SitewebPersistentService(id, name, description, resource);

            // 手动注入ApplicationContext
            if (applicationContext != null) {
                service.setApplicationContext(applicationContext);
                logger.debug("ApplicationContext injected into SitewebPersistentService: {}", id);
            } else {
                logger.warn("ApplicationContext is null, SitewebPersistentService may not work properly: {}", id);
            }

            logger.info("Successfully created SitewebPersistentService: {} with resource: {} ({})", id, resource.getId(), resourceType);
            return service;

        } catch (Exception e) {
            logger.error("Failed to create SitewebPersistentService: {}", id, e);
            throw new MiddlewareTechnicalException(
                com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to create SitewebPersistentService: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 判断资源类型是否支持
     *
     * @param resourceType 资源类型
     * @return 是否支持
     */
    private boolean isSupportedResourceType(String resourceType) {
        // 支持的关系型数据库资源类型
        return "MYSQL".equalsIgnoreCase(resourceType) ||
               "H2".equalsIgnoreCase(resourceType) ||
               "POSTGRESQL".equalsIgnoreCase(resourceType);
    }

    @Override
    public void destroyService(Service service) throws MiddlewareTechnicalException {
        try {
            if (service == null) {
                logger.warn("Attempting to destroy null service");
                return;
            }

            if (!(service instanceof SitewebPersistentService)) {
                throw new MiddlewareTechnicalException(
                    com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                    "Service is not a SitewebPersistentService: " + service.getClass().getName()
                );
            }

            logger.info("Destroying SitewebPersistentService: {}", service.getId());

            // 停止服务
            if (service.getStatus() == com.siteweb.tcs.middleware.common.service.ServiceStatus.STARTED) {
                service.stop();
            }

            // 销毁服务
            service.destroy();

            logger.info("Successfully destroyed SitewebPersistentService: {}", service.getId());

        } catch (Exception e) {
            logger.error("Failed to destroy SitewebPersistentService: {}", service != null ? service.getId() : "null", e);
            throw new MiddlewareTechnicalException(
                com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
                "Failed to destroy SitewebPersistentService: " + e.getMessage(),
                e
            );
        }
    }
}

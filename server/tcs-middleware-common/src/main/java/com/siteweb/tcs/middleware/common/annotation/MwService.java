package com.siteweb.tcs.middleware.common.annotation;

import com.siteweb.tcs.middleware.common.service.ServiceType;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 服务注入注解
 */
@Target({ElementType.FIELD, ElementType.METHOD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface MwService {

    /**
     * 服务ID
     * 当type为SITEWEB_PERSISTENT时，此值可以为空，将使用默认服务
     */
    String value() default "";

    /**
     * 是否必须
     */
    boolean required() default true;

    /**
     * 服务类型
     * 当指定为SITEWEB_PERSISTENT时，不需要配置服务ID，将自动创建默认的SitewebPersistentService
     */
    ServiceType type() default ServiceType.NONE;
}

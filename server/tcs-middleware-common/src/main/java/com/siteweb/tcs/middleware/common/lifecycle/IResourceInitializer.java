package com.siteweb.tcs.middleware.common.lifecycle;

import com.siteweb.tcs.middleware.common.resource.Resource;

/**
 * 资源初始化器接口
 * 用于初始化资源实例
 */
public interface IResourceInitializer {

    /**
     * 通过资源配置ID初始化资源
     *
     * @param resourceConfigurationId 资源配置ID
     * @return 资源实例
     * @throws Exception 初始化异常
     */
    Resource initializeResourceById(String resourceConfigurationId) throws Exception;
}

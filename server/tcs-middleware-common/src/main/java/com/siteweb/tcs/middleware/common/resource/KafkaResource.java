package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.DescribeClusterResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.common.Node;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.kafka.core.KafkaTemplate;
import org.springframework.kafka.core.ProducerFactory;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Kafka资源
 */
public class KafkaResource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(KafkaResource.class);

    private KafkaTemplate<String, String> kafkaTemplate;
    private ProducerFactory<String, String> producerFactory;
    private Properties producerProperties;

    /**
     * 构造函数
     *
     * @param id              资源ID
     * @param type            资源类型
     * @param name            资源名称
     * @param description     资源描述
     * @param kafkaTemplate   Kafka模板
     * @param producerFactory 生产者工厂
     * @param producerProperties 生产者配置属性
     */
    public KafkaResource(String id, String type, String name, String description,
                         KafkaTemplate<String, String> kafkaTemplate,
                         ProducerFactory<String, String> producerFactory,
                         Properties producerProperties) {
        super(id, type, name, description);
        this.kafkaTemplate = kafkaTemplate;
        this.producerFactory = producerFactory;
        this.producerProperties = producerProperties;
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化Kafka资源: {}", getId());
        // 初始化阶段不创建客户端，只在启动时创建
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动Kafka资源: {}", getId());
        try {
            // 测试连接
            if (kafkaTemplate == null) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.RESOURCE_CONFIG_INVALID,
                    "Kafka模板未初始化"
                );
            }

            // 测试连接
            testConnection();

            logger.info("Kafka资源启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动Kafka资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动Kafka资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止Kafka资源: {}", getId());
        try {
            // 清空引用，允许GC回收
            kafkaTemplate = null;
            producerFactory = null;
            producerProperties = null;
            logger.info("Kafka资源停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止Kafka资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止Kafka资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁Kafka资源: {}", getId());
        try {
            // 清空引用，允许GC回收
            kafkaTemplate = null;
            producerFactory = null;
            producerProperties = null;
            logger.info("Kafka资源销毁成功: {}", getId());
        } catch (Exception e) {
            logger.error("销毁Kafka资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁Kafka资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (kafkaTemplate == null) {
            return HealthStatus.down("Kafka模板未初始化");
        }

        try {
            // 创建AdminClient用于检查Kafka集群状态
            try (AdminClient adminClient = AdminClient.create(producerProperties)) {
                // 获取集群信息
                DescribeClusterResult clusterResult = adminClient.describeCluster();
                Collection<Node> nodes = clusterResult.nodes().get(10, TimeUnit.SECONDS);
                String clusterId = clusterResult.clusterId().get(10, TimeUnit.SECONDS);

                // 获取主题列表
                ListTopicsResult topicsResult = adminClient.listTopics();
                int topicCount = topicsResult.names().get(10, TimeUnit.SECONDS).size();

                // 构建健康状态详情
                Map<String, Object> details = new HashMap<>();
                details.put("clusterId", clusterId);
                details.put("nodeCount", nodes.size());
                details.put("topicCount", topicCount);
                details.put("bootstrapServers", producerProperties.getProperty("bootstrap.servers"));

                return HealthStatus.up("连接正常", details);
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            logger.error("检查Kafka资源健康状态被中断: {}", getId(), e);
            return HealthStatus.down("连接检查被中断: " + e.getMessage());
        } catch (ExecutionException | TimeoutException e) {
            logger.error("检查Kafka资源健康状态失败: {}", getId(), e);
            return HealthStatus.down("连接失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("检查Kafka资源健康状态异常: {}", getId(), e);
            return HealthStatus.down("连接异常: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        if (kafkaTemplate == null) {
            throw new IllegalStateException("Kafka资源未启动");
        }
        return (T) kafkaTemplate;
    }

    /**
     * 获取Kafka模板
     *
     * @return Kafka模板
     */
    public KafkaTemplate<String, String> getKafkaTemplate() {
        if (kafkaTemplate == null) {
            throw new IllegalStateException("Kafka资源未启动");
        }
        return kafkaTemplate;
    }

    /**
     * 测试Kafka连接
     *
     * @throws MiddlewareTechnicalException 如果连接测试失败
     */
    private void testConnection() throws MiddlewareTechnicalException {
        try (AdminClient adminClient = AdminClient.create(producerProperties)) {
            // 尝试获取集群信息，超时时间10秒
            adminClient.describeCluster().clusterId().get(10, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.MESSAGE_QUEUE_CONNECTION_FAILED,
                "Kafka连接测试被中断: " + e.getMessage(),
                e
            );
        } catch (ExecutionException | TimeoutException e) {
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.MESSAGE_QUEUE_CONNECTION_FAILED,
                "Kafka连接测试失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 发送消息到指定主题
     *
     * @param topic 主题
     * @param message 消息内容
     * @return 发送结果
     */
    public org.springframework.kafka.support.SendResult<String, String> send(String topic, String message) {
        try {
            return kafkaTemplate.send(topic, message).get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("发送消息被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("发送消息失败", e);
        }
    }

    /**
     * 发送消息到指定主题和分区
     *
     * @param topic 主题
     * @param partition 分区
     * @param key 键
     * @param message 消息内容
     * @return 发送结果
     */
    public org.springframework.kafka.support.SendResult<String, String> send(String topic, Integer partition, String key, String message) {
        try {
            return kafkaTemplate.send(topic, partition, key, message).get();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("发送消息被中断", e);
        } catch (ExecutionException e) {
            throw new RuntimeException("发送消息失败", e);
        }
    }
}

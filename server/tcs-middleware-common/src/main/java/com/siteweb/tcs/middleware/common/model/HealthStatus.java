package com.siteweb.tcs.middleware.common.model;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 健康状态类
 */
public class HealthStatus {
    
    /**
     * 健康状态枚举
     */
    public enum Status {
        /**
         * 健康
         */
        UP,
        
        /**
         * 不健康
         */
        DOWN,
        
        /**
         * 未知
         */
        UNKNOWN
    }
    
    private final Status status;
    private final String message;
    private final Map<String, Object> details;
    
    /**
     * 构造函数
     * 
     * @param status 健康状态
     * @param message 健康状态消息
     */
    public HealthStatus(Status status, String message) {
        this(status, message, Collections.emptyMap());
    }
    
    /**
     * 构造函数
     * 
     * @param status 健康状态
     * @param message 健康状态消息
     * @param details 健康状态详情
     */
    public HealthStatus(Status status, String message, Map<String, Object> details) {
        this.status = status;
        this.message = message;
        this.details = new HashMap<>(details);
    }
    
    /**
     * 获取健康状态
     * 
     * @return 健康状态
     */
    public Status getStatus() {
        return status;
    }
    
    /**
     * 获取健康状态消息
     * 
     * @return 健康状态消息
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * 获取健康状态详情
     * 
     * @return 健康状态详情
     */
    public Map<String, Object> getDetails() {
        return Collections.unmodifiableMap(details);
    }
    
    /**
     * 判断是否健康
     * 
     * @return 是否健康
     */
    public boolean isUp() {
        return Status.UP.equals(status);
    }
    
    /**
     * 创建健康状态
     * 
     * @param message 健康状态消息
     * @return 健康状态
     */
    public static HealthStatus up(String message) {
        return new HealthStatus(Status.UP, message);
    }
    
    /**
     * 创建健康状态
     * 
     * @param message 健康状态消息
     * @param details 健康状态详情
     * @return 健康状态
     */
    public static HealthStatus up(String message, Map<String, Object> details) {
        return new HealthStatus(Status.UP, message, details);
    }
    
    /**
     * 创建不健康状态
     * 
     * @param message 健康状态消息
     * @return 健康状态
     */
    public static HealthStatus down(String message) {
        return new HealthStatus(Status.DOWN, message);
    }
    
    /**
     * 创建不健康状态
     * 
     * @param message 健康状态消息
     * @param details 健康状态详情
     * @return 健康状态
     */
    public static HealthStatus down(String message, Map<String, Object> details) {
        return new HealthStatus(Status.DOWN, message, details);
    }
    
    /**
     * 创建未知状态
     * 
     * @param message 健康状态消息
     * @return 健康状态
     */
    public static HealthStatus unknown(String message) {
        return new HealthStatus(Status.UNKNOWN, message);
    }
    
    /**
     * 创建未知状态
     * 
     * @param message 健康状态消息
     * @param details 健康状态详情
     * @return 健康状态
     */
    public static HealthStatus unknown(String message, Map<String, Object> details) {
        return new HealthStatus(Status.UNKNOWN, message, details);
    }
}

package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.sql.DataSource;
import java.sql.Connection;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

/**
 * H2数据库资源
 */
public class H2Resource extends BaseResource {

    private static final Logger logger = LoggerFactory.getLogger(H2Resource.class);

    private DataSource dataSource;
    private HikariConfig hikariConfig;
    private volatile boolean dataSourceClosed = false;

    /**
     * 构造函数
     *
     * @param id           资源ID
     * @param type         资源类型
     * @param name         资源名称
     * @param description  资源描述
     * @param dataSource   数据源
     */
    public H2Resource(String id, String type, String name, String description, DataSource dataSource) {
        super(id, type, name, description);
        this.dataSource = dataSource;

        // 如果是HikariDataSource，保存配置以便重新创建
        if (dataSource instanceof HikariDataSource) {
            HikariDataSource hikariDataSource = (HikariDataSource) dataSource;
            this.hikariConfig = new HikariConfig();
            this.hikariConfig.setJdbcUrl(hikariDataSource.getJdbcUrl());
            this.hikariConfig.setUsername(hikariDataSource.getUsername());
            this.hikariConfig.setPassword(hikariDataSource.getPassword());
            this.hikariConfig.setDriverClassName(hikariDataSource.getDriverClassName());
            this.hikariConfig.setPoolName(hikariDataSource.getPoolName());
            this.hikariConfig.setMinimumIdle(hikariDataSource.getMinimumIdle());
            this.hikariConfig.setMaximumPoolSize(hikariDataSource.getMaximumPoolSize());
            this.hikariConfig.setConnectionTimeout(hikariDataSource.getConnectionTimeout());
            this.hikariConfig.setIdleTimeout(hikariDataSource.getIdleTimeout());
            this.hikariConfig.setMaxLifetime(hikariDataSource.getMaxLifetime());
        }
    }

    @Override
    protected void doInitialize() throws MiddlewareTechnicalException {
        logger.debug("初始化H2资源: {}", getId());
        // 初始化阶段不创建数据源，只在启动时创建
    }

    @Override
    protected void doStart() throws MiddlewareTechnicalException {
        logger.debug("启动H2资源: {}", getId());
        try {
            // 如果DataSource已关闭，重新创建
            if (dataSourceClosed && hikariConfig != null) {
                logger.info("检测到DataSource已关闭，重新创建: {}", getId());
                recreateDataSource();
            }

            // 测试连接
            try (Connection connection = dataSource.getConnection()) {
                if (!connection.isValid(5)) {
                    throw new SQLException("连接无效");
                }
            }

            dataSourceClosed = false;
            logger.info("H2资源启动成功: {}", getId());
        } catch (Exception e) {
            logger.error("启动H2资源失败: {}", getId(), e);

            // 如果连接失败且有配置，尝试重新创建DataSource
            if (hikariConfig != null && !dataSourceClosed) {
                try {
                    logger.info("尝试重新创建DataSource: {}", getId());
                    recreateDataSource();

                    // 再次测试连接
                    try (Connection connection = dataSource.getConnection()) {
                        if (connection.isValid(5)) {
                            dataSourceClosed = false;
                            logger.info("H2资源重新创建后启动成功: {}", getId());
                            return;
                        }
                    }
                } catch (Exception recreateException) {
                    logger.error("重新创建DataSource失败: {}", getId(), recreateException);
                }
            }

            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_START_FAILED,
                "启动H2资源失败: " + e.getMessage(),
                e
            );
        }
    }

    /**
     * 重新创建DataSource
     */
    public void recreateDataSource() {
        if (hikariConfig == null) {
            throw new IllegalStateException("无法重新创建DataSource，缺少配置信息");
        }

        // 关闭旧的DataSource
        if (dataSource instanceof AutoCloseable) {
            try {
                ((AutoCloseable) dataSource).close();
            } catch (Exception e) {
                logger.warn("关闭旧DataSource时发生异常: {}", getId(), e);
            }
        }

        // 创建新的DataSource
        this.dataSource = new HikariDataSource(hikariConfig);
        this.dataSourceClosed = false;
        logger.info("DataSource重新创建完成: {}", getId());
    }

    @Override
    protected void doStop() throws MiddlewareTechnicalException {
        logger.debug("停止H2资源: {}", getId());
        try {
            // 对于HikariDataSource，需要关闭连接池
            if (dataSource instanceof AutoCloseable) {
                ((AutoCloseable) dataSource).close();
            }
            logger.info("H2资源停止成功: {}", getId());
        } catch (Exception e) {
            logger.error("停止H2资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_STOP_FAILED,
                "停止H2资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    protected void doDestroy() throws MiddlewareTechnicalException {
        logger.debug("销毁H2资源: {}", getId());
        try {
            // 对于HikariDataSource，需要关闭连接池
            if (dataSource instanceof AutoCloseable) {
                ((AutoCloseable) dataSource).close();
            }
            dataSource = null;
            logger.info("H2资源销毁成功: {}", getId());
        } catch (Exception e) {
            logger.error("销毁H2资源失败: {}", getId(), e);
            throw new MiddlewareTechnicalException(
                MiddlewareTechnicalErrorCode.RESOURCE_DESTROY_FAILED,
                "销毁H2资源失败: " + e.getMessage(),
                e
            );
        }
    }

    @Override
    public HealthStatus checkHealth() {
        if (dataSource == null) {
            return HealthStatus.down("数据源未初始化");
        }

        // 如果DataSource已标记为关闭，返回停止状态
        if (dataSourceClosed) {
            return HealthStatus.down("数据源已停止");
        }

        try (Connection connection = dataSource.getConnection()) {
            if (connection.isValid(5)) {
                Map<String, Object> details = new HashMap<>();
                // 尝试获取连接池信息
                try {
                    // 反射获取HikariDataSource的连接池信息
                    Object poolMXBean = dataSource.getClass().getMethod("getHikariPoolMXBean").invoke(dataSource);
                    if (poolMXBean != null) {
                        details.put("activeConnections", poolMXBean.getClass().getMethod("getActiveConnections").invoke(poolMXBean));
                        details.put("idleConnections", poolMXBean.getClass().getMethod("getIdleConnections").invoke(poolMXBean));
                        details.put("totalConnections", poolMXBean.getClass().getMethod("getTotalConnections").invoke(poolMXBean));
                        details.put("threadsAwaitingConnection", poolMXBean.getClass().getMethod("getThreadsAwaitingConnection").invoke(poolMXBean));
                    }
                } catch (Exception e) {
                    // 忽略反射异常，这只是尝试获取额外信息
                    logger.debug("获取连接池信息失败", e);
                }

                return HealthStatus.up("连接正常", details);
            } else {
                return HealthStatus.down("连接无效");
            }
        } catch (SQLException e) {
            logger.error("检查H2资源健康状态失败: {}", getId(), e);

            // 如果是因为DataSource关闭导致的异常，标记为已关闭
            if (e.getMessage() != null && e.getMessage().contains("has been closed")) {
                dataSourceClosed = true;
                return HealthStatus.down("数据源已关闭，需要重启");
            }

            return HealthStatus.down("连接失败: " + e.getMessage());
        }
    }

    @Override
    @SuppressWarnings("unchecked")
    public <T> T getNativeResource() {
        if (dataSource == null) {
            throw new IllegalStateException("H2资源未启动");
        }
        return (T) dataSource;
    }

    /**
     * 获取数据源
     *
     * @return 数据源
     */
    public DataSource getDataSource() {
        if (dataSource == null) {
            throw new IllegalStateException("H2资源未启动");
        }
        return dataSource;
    }
}

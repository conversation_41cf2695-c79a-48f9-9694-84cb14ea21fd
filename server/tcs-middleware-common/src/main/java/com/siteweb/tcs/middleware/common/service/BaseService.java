package com.siteweb.tcs.middleware.common.service;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareTechnicalErrorCode;
import com.siteweb.tcs.middleware.common.resource.Resource;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 服务基础抽象类
 * 提供Service接口的基础实现
 */
public abstract class BaseService implements Service {

    protected final String id;
    protected final String type;
    protected final String name;
    protected final String description;
    protected final Resource resource;
    protected final AtomicReference<ServiceStatus> status = new AtomicReference<>(ServiceStatus.INITIALIZED);

    /**
     * 构造函数
     *
     * @param id 服务ID
     * @param type 服务类型
     * @param name 服务名称
     * @param description 服务描述
     * @param resource 关联的资源
     */
    protected BaseService(String id, String type, String name, String description, Resource resource) {
        this.id = id;
        this.type = type;
        this.name = name;
        this.description = description;
        this.resource = resource;
    }

    @Override
    public String getId() {
        return id;
    }

    @Override
    public String getType() {
        return type;
    }

    @Override
    public String getName() {
        return name;
    }

    @Override
    public String getDescription() {
        return description;
    }

    @Override
    public ServiceStatus getStatus() {
        return status.get();
    }

    @Override
    public Resource getResource() {
        return resource;
    }

    @Override
    public void initialize() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ServiceStatus.INITIALIZED, ServiceStatus.INITIALIZED)) {
            doInitialize();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Service already initialized: " + id
            );
        }
    }

    @Override
    public void start() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ServiceStatus.INITIALIZED, ServiceStatus.STARTED) ||
            status.compareAndSet(ServiceStatus.STOPPED, ServiceStatus.STARTED)) {
            doStart();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Service cannot be started: " + id + ", current status: " + status.get()
            );
        }
    }

    @Override
    public void stop() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ServiceStatus.STARTED, ServiceStatus.STOPPED)) {
            doStop();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Service cannot be stopped: " + id + ", current status: " + status.get()
            );
        }
    }

    @Override
    public void destroy() throws MiddlewareTechnicalException {
        if (status.compareAndSet(ServiceStatus.STOPPED, ServiceStatus.DESTROYED) ||
            status.compareAndSet(ServiceStatus.INITIALIZED, ServiceStatus.DESTROYED)) {
            doDestroy();
        } else {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.OPERATION_NOT_ALLOWED,
                "Service cannot be destroyed: " + id + ", current status: " + status.get()
            );
        }
    }

    @Override
    public boolean isHealthy() {
        return checkHealth().isUp();
    }

    /**
     * 执行初始化操作
     *
     * @throws MiddlewareTechnicalException 初始化失败时抛出异常
     */
    protected abstract void doInitialize() throws MiddlewareTechnicalException;

    /**
     * 执行启动操作
     *
     * @throws MiddlewareTechnicalException 启动失败时抛出异常
     */
    protected abstract void doStart() throws MiddlewareTechnicalException;

    /**
     * 执行停止操作
     *
     * @throws MiddlewareTechnicalException 停止失败时抛出异常
     */
    protected abstract void doStop() throws MiddlewareTechnicalException;

    /**
     * 执行销毁操作
     *
     * @throws MiddlewareTechnicalException 销毁失败时抛出异常
     */
    protected abstract void doDestroy() throws MiddlewareTechnicalException;
}

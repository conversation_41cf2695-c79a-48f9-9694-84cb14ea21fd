package com.siteweb.tcs.middleware.common.annotation;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessErrorCode;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

/**
 * 服务注解处理器
 */
@Component
public class MwServiceAnnotationProcessor implements BeanPostProcessor {

    private static final Logger logger = LoggerFactory.getLogger(MwServiceAnnotationProcessor.class);

    @Autowired
    @Lazy
    private ServiceRegistry serviceRegistry;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        processFields(bean);
        processMethods(bean);
        return bean;
    }

    private void processFields(Object bean) {
        ReflectionUtils.doWithFields(bean.getClass(), field -> {
            MwService annotation = field.getAnnotation(MwService.class);
            if (annotation != null) {
                Service service = getService(annotation);
                if (service != null) {
                    ReflectionUtils.makeAccessible(field);
                    ReflectionUtils.setField(field, bean, service);
                    logger.debug("Injected service {} into field {} of bean {}", service.getId(), field.getName(), bean.getClass().getName());
                }
            }
        });
    }

    private void processMethods(Object bean) {
        ReflectionUtils.doWithMethods(bean.getClass(), method -> {
            MwService annotation = method.getAnnotation(MwService.class);
            if (annotation != null && method.getParameterCount() == 1) {
                Service service = getService(annotation);
                if (service != null) {
                    ReflectionUtils.makeAccessible(method);
                    ReflectionUtils.invokeMethod(method, bean, service);
                    logger.debug("Injected service {} into method {} of bean {}", service.getId(), method.getName(), bean.getClass().getName());
                }
            }
        });
    }

    private Service getService(MwService annotation) {
        String serviceId = annotation.value();
        boolean required = annotation.required();

        Service service = null;

        if (StringUtils.hasText(serviceId)) {
            // 根据服务ID获取服务
            service = serviceRegistry.get(serviceId);
            if (service == null && required) {
                throw new MiddlewareBusinessException(
                    MiddlewareBusinessErrorCode.SERVICE_NOT_FOUND,
                    "Service not found: " + serviceId
                );
            }
        } else if (required) {
            throw new MiddlewareBusinessException(
                MiddlewareBusinessErrorCode.SERVICE_NOT_FOUND,
                "Service ID is required"
            );
        }

        return service;
    }
}

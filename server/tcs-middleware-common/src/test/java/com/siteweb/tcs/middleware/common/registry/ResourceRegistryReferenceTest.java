package com.siteweb.tcs.middleware.common.registry;

import com.siteweb.tcs.middleware.common.resource.H2Resource;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import javax.sql.DataSource;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ResourceRegistry引用计数测试
 */
public class ResourceRegistryReferenceTest {

    private ResourceRegistry resourceRegistry;
    private H2Resource testResource;
    private String resourceId = "test-h2-resource";

    @BeforeEach
    public void setUp() throws Exception {
        resourceRegistry = new ResourceRegistry();
        
        // 创建测试用的H2Resource
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:h2:mem:test_reference;DB_CLOSE_DELAY=-1;MODE=MySQL");
        config.setUsername("sa");
        config.setPassword("");
        config.setDriverClassName("org.h2.Driver");
        config.setPoolName("test-reference-pool");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        testResource = new H2Resource(
            resourceId,
            "h2",
            "测试引用计数资源",
            "用于测试引用计数功能的H2资源",
            dataSource
        );
        
        testResource.initialize();
        testResource.start();
        resourceRegistry.save(testResource);
    }

    @Test
    public void testAddResourceReference() {
        // 初始状态：无引用
        assertEquals(0, resourceRegistry.getResourceReferenceCount(resourceId));
        assertTrue(resourceRegistry.getResourceReferences(resourceId).isEmpty());
        
        // 添加第一个引用
        resourceRegistry.addResourceReference(resourceId, "plugin-a");
        assertEquals(1, resourceRegistry.getResourceReferenceCount(resourceId));
        assertTrue(resourceRegistry.getResourceReferences(resourceId).contains("plugin-a"));
        
        // 添加第二个引用
        resourceRegistry.addResourceReference(resourceId, "plugin-b");
        assertEquals(2, resourceRegistry.getResourceReferenceCount(resourceId));
        Set<String> references = resourceRegistry.getResourceReferences(resourceId);
        assertTrue(references.contains("plugin-a"));
        assertTrue(references.contains("plugin-b"));
        
        // 重复添加同一个引用
        resourceRegistry.addResourceReference(resourceId, "plugin-a");
        assertEquals(3, resourceRegistry.getResourceReferenceCount(resourceId));
    }

    @Test
    public void testRemoveResourceReference() {
        // 先添加两个引用
        resourceRegistry.addResourceReference(resourceId, "plugin-a");
        resourceRegistry.addResourceReference(resourceId, "plugin-b");
        assertEquals(2, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 移除第一个引用
        boolean canDestroy = resourceRegistry.removeResourceReference(resourceId, "plugin-a");
        assertFalse(canDestroy); // 还有其他引用，不能销毁
        assertEquals(1, resourceRegistry.getResourceReferenceCount(resourceId));
        assertFalse(resourceRegistry.getResourceReferences(resourceId).contains("plugin-a"));
        assertTrue(resourceRegistry.getResourceReferences(resourceId).contains("plugin-b"));
        
        // 移除最后一个引用
        canDestroy = resourceRegistry.removeResourceReference(resourceId, "plugin-b");
        assertTrue(canDestroy); // 没有引用了，可以销毁
        assertEquals(0, resourceRegistry.getResourceReferenceCount(resourceId));
        assertTrue(resourceRegistry.getResourceReferences(resourceId).isEmpty());
    }

    @Test
    public void testGetDataSourceWithReference() throws Exception {
        // 使用带引用的方法获取DataSource
        DataSource dataSource1 = resourceRegistry.getDataSource(resourceId, "plugin-a");
        assertNotNull(dataSource1);
        assertEquals(1, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 另一个插件也获取DataSource
        DataSource dataSource2 = resourceRegistry.getDataSource(resourceId, "plugin-b");
        assertNotNull(dataSource2);
        assertSame(dataSource1, dataSource2); // 应该是同一个DataSource
        assertEquals(2, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 验证引用者列表
        Set<String> references = resourceRegistry.getResourceReferences(resourceId);
        assertTrue(references.contains("plugin-a"));
        assertTrue(references.contains("plugin-b"));
    }

    @Test
    public void testMultiplePluginsScenario() throws Exception {
        // 模拟多个插件使用同一个资源的场景
        
        // 插件A启动，获取DataSource
        DataSource dsA = resourceRegistry.getDataSource(resourceId, "plugin-a");
        assertNotNull(dsA);
        assertEquals(1, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 插件B启动，获取DataSource
        DataSource dsB = resourceRegistry.getDataSource(resourceId, "plugin-b");
        assertNotNull(dsB);
        assertSame(dsA, dsB);
        assertEquals(2, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 插件A停止，移除引用
        boolean canDestroyA = resourceRegistry.removeResourceReference(resourceId, "plugin-a");
        assertFalse(canDestroyA); // 插件B还在使用，不能销毁
        assertEquals(1, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 验证DataSource仍然可用
        assertTrue(dsB.getConnection().isValid(5));
        
        // 插件B停止，移除引用
        boolean canDestroyB = resourceRegistry.removeResourceReference(resourceId, "plugin-b");
        assertTrue(canDestroyB); // 没有插件使用了，可以销毁
        assertEquals(0, resourceRegistry.getResourceReferenceCount(resourceId));
    }

    @Test
    public void testRemoveNonExistentReference() {
        // 移除不存在的引用
        boolean canDestroy = resourceRegistry.removeResourceReference(resourceId, "non-existent-plugin");
        assertFalse(canDestroy);
        assertEquals(0, resourceRegistry.getResourceReferenceCount(resourceId));
        
        // 添加一个引用后再移除不存在的引用
        resourceRegistry.addResourceReference(resourceId, "plugin-a");
        canDestroy = resourceRegistry.removeResourceReference(resourceId, "non-existent-plugin");
        assertFalse(canDestroy);
        assertEquals(1, resourceRegistry.getResourceReferenceCount(resourceId));
    }

    @Test
    public void testGetDataSourceWithoutReference() throws Exception {
        // 使用不带引用的方法获取DataSource
        DataSource dataSource = resourceRegistry.getDataSource(resourceId);
        assertNotNull(dataSource);
        assertEquals(0, resourceRegistry.getResourceReferenceCount(resourceId)); // 不应该增加引用计数
    }
}

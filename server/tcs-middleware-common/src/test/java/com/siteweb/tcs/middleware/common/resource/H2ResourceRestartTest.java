package com.siteweb.tcs.middleware.common.resource;

import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.junit.jupiter.api.Test;

import javax.sql.DataSource;
import java.sql.Connection;

import static org.junit.jupiter.api.Assertions.*;

/**
 * H2Resource重启测试
 * 验证插件停止/启动时DataSource的处理
 */
public class H2ResourceRestartTest {

    @Test
    public void testResourceRestartAfterStop() throws Exception {
        // 创建H2内存数据库配置
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:h2:mem:test_restart;DB_CLOSE_DELAY=-1;MODE=MySQL");
        config.setUsername("sa");
        config.setPassword("");
        config.setDriverClassName("org.h2.Driver");
        config.setPoolName("test-restart-pool");
        config.setMinimumIdle(1);
        config.setMaximumPoolSize(5);
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        // 创建H2Resource
        H2Resource resource = new H2Resource(
            "test-restart-resource",
            "h2",
            "测试重启资源",
            "用于测试重启功能的H2资源",
            dataSource
        );
        
        // 1. 初始化和启动资源
        resource.initialize();
        resource.start();
        
        // 验证资源正常工作
        HealthStatus healthBefore = resource.checkHealth();
        assertTrue(healthBefore.isHealthy(), "资源应该是健康的");
        
        // 验证可以获取连接
        DataSource resourceDataSource = resource.getDataSource();
        try (Connection conn = resourceDataSource.getConnection()) {
            assertTrue(conn.isValid(5), "连接应该是有效的");
        }
        
        // 2. 停止资源（模拟插件停止）
        resource.stop();
        
        // 验证健康检查显示已停止
        HealthStatus healthAfterStop = resource.checkHealth();
        assertFalse(healthAfterStop.isHealthy(), "停止后资源应该不健康");
        assertEquals("数据源已停止", healthAfterStop.getMessage());
        
        // 3. 重新启动资源（模拟插件重启）
        resource.start();
        
        // 验证资源重新正常工作
        HealthStatus healthAfterRestart = resource.checkHealth();
        assertTrue(healthAfterRestart.isHealthy(), "重启后资源应该是健康的");
        
        // 验证可以重新获取连接
        DataSource restartedDataSource = resource.getDataSource();
        try (Connection conn = restartedDataSource.getConnection()) {
            assertTrue(conn.isValid(5), "重启后连接应该是有效的");
        }
        
        // 4. 清理
        resource.stop();
        resource.destroy();
    }
    
    @Test
    public void testResourceRecreateAfterDataSourceClosed() throws Exception {
        // 创建H2内存数据库配置
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:h2:mem:test_recreate;DB_CLOSE_DELAY=-1;MODE=MySQL");
        config.setUsername("sa");
        config.setPassword("");
        config.setDriverClassName("org.h2.Driver");
        config.setPoolName("test-recreate-pool");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        // 创建H2Resource
        H2Resource resource = new H2Resource(
            "test-recreate-resource",
            "h2",
            "测试重建资源",
            "用于测试DataSource重建功能的H2资源",
            dataSource
        );
        
        // 1. 初始化和启动资源
        resource.initialize();
        resource.start();
        
        // 验证资源正常工作
        assertTrue(resource.checkHealth().isHealthy());
        
        // 2. 手动关闭DataSource（模拟外部关闭）
        dataSource.close();
        
        // 3. 健康检查应该检测到DataSource已关闭
        HealthStatus healthAfterClose = resource.checkHealth();
        assertFalse(healthAfterClose.isHealthy());
        assertTrue(healthAfterClose.getMessage().contains("已关闭"));
        
        // 4. 重新启动应该重建DataSource
        resource.start();
        
        // 验证资源重新正常工作
        HealthStatus healthAfterRecreate = resource.checkHealth();
        assertTrue(healthAfterRecreate.isHealthy(), "重建后资源应该是健康的");
        
        // 验证可以获取新的连接
        DataSource recreatedDataSource = resource.getDataSource();
        try (Connection conn = recreatedDataSource.getConnection()) {
            assertTrue(conn.isValid(5), "重建后连接应该是有效的");
        }
        
        // 5. 清理
        resource.stop();
        resource.destroy();
    }
    
    @Test
    public void testMultipleRestartCycles() throws Exception {
        // 创建H2内存数据库配置
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl("jdbc:h2:mem:test_multiple;DB_CLOSE_DELAY=-1;MODE=MySQL");
        config.setUsername("sa");
        config.setPassword("");
        config.setDriverClassName("org.h2.Driver");
        config.setPoolName("test-multiple-pool");
        
        HikariDataSource dataSource = new HikariDataSource(config);
        
        // 创建H2Resource
        H2Resource resource = new H2Resource(
            "test-multiple-resource",
            "h2",
            "测试多次重启资源",
            "用于测试多次重启功能的H2资源",
            dataSource
        );
        
        resource.initialize();
        
        // 进行多次启动/停止循环
        for (int i = 0; i < 3; i++) {
            // 启动
            resource.start();
            assertTrue(resource.checkHealth().isHealthy(), "第" + (i+1) + "次启动后应该健康");
            
            // 验证连接
            try (Connection conn = resource.getDataSource().getConnection()) {
                assertTrue(conn.isValid(5), "第" + (i+1) + "次启动后连接应该有效");
            }
            
            // 停止
            resource.stop();
            assertFalse(resource.checkHealth().isHealthy(), "第" + (i+1) + "次停止后应该不健康");
        }
        
        // 最终清理
        resource.destroy();
    }
}

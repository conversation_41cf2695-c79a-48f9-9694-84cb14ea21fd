package com.siteweb.tcs.middleware.common.runtime;

import org.junit.jupiter.api.Test;
import org.slf4j.Logger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ComponentLogger测试类
 */
public class ComponentLoggerTest {

    @Test
    public void testResourceLogger() {
        String resourceId = "test-database-001";
        Logger logger = ComponentLogger.getResourceLogger(resourceId);
        
        assertNotNull(logger);
        assertEquals("middleware.resource." + resourceId, logger.getName());
        
        // 测试日志记录
        logger.info("Resource {} initialized", resourceId);
        logger.warn("Resource {} configuration warning", resourceId);
        logger.error("Resource {} connection failed", resourceId);
    }

    @Test
    public void testServiceLogger() {
        String serviceId = "test-http-service-001";
        Logger logger = ComponentLogger.getServiceLogger(serviceId);
        
        assertNotNull(logger);
        assertEquals("middleware.service." + serviceId, logger.getName());
        
        // 测试日志记录
        logger.info("Service {} started", serviceId);
        logger.warn("Service {} performance warning", serviceId);
        logger.error("Service {} request failed", serviceId);
    }

    @Test
    public void testResourceLoggerWithClass() {
        String resourceId = "test-database-002";
        Logger logger = ComponentLogger.getResourceLogger(resourceId, ComponentLoggerTest.class);
        
        assertNotNull(logger);
        assertEquals("middleware.resource." + resourceId + ".ComponentLoggerTest", logger.getName());
        
        // 测试日志记录
        logger.info("Resource implementation {} loaded", resourceId);
    }

    @Test
    public void testServiceLoggerWithClass() {
        String serviceId = "test-http-service-002";
        Logger logger = ComponentLogger.getServiceLogger(serviceId, ComponentLoggerTest.class);
        
        assertNotNull(logger);
        assertEquals("middleware.service." + serviceId + ".ComponentLoggerTest", logger.getName());
        
        // 测试日志记录
        logger.info("Service implementation {} loaded", serviceId);
    }

    @Test
    public void testResourceLifecycleLogging() {
        String resourceId = "test-lifecycle-resource";
        
        // 测试不同级别的生命周期日志
        ComponentLogger.logResourceLifecycle(resourceId, "INFO", "Resource initialized");
        ComponentLogger.logResourceLifecycle(resourceId, "WARN", "Resource configuration issue: {}", "timeout too low");
        ComponentLogger.logResourceLifecycle(resourceId, "ERROR", "Resource failed to start: {}", "connection refused");
        ComponentLogger.logResourceLifecycle(resourceId, "DEBUG", "Resource debug info: {}", "connection pool size: 10");
        
        // 测试无效级别（应该默认为INFO）
        ComponentLogger.logResourceLifecycle(resourceId, "INVALID", "This should be INFO level");
    }

    @Test
    public void testServiceLifecycleLogging() {
        String serviceId = "test-lifecycle-service";
        
        // 测试不同级别的生命周期日志
        ComponentLogger.logServiceLifecycle(serviceId, "INFO", "Service initialized");
        ComponentLogger.logServiceLifecycle(serviceId, "WARN", "Service performance warning: {}", "high latency");
        ComponentLogger.logServiceLifecycle(serviceId, "ERROR", "Service failed to process request: {}", "invalid input");
        ComponentLogger.logServiceLifecycle(serviceId, "DEBUG", "Service debug info: {}", "request count: 100");
        
        // 测试无效级别（应该默认为INFO）
        ComponentLogger.logServiceLifecycle(serviceId, "INVALID", "This should be INFO level");
    }

    @Test
    public void testLoggerNameConsistency() {
        String resourceId = "consistency-test-resource";
        String serviceId = "consistency-test-service";
        
        // 确保相同ID的Logger是同一个实例
        Logger logger1 = ComponentLogger.getResourceLogger(resourceId);
        Logger logger2 = ComponentLogger.getResourceLogger(resourceId);
        assertSame(logger1, logger2);
        
        Logger serviceLogger1 = ComponentLogger.getServiceLogger(serviceId);
        Logger serviceLogger2 = ComponentLogger.getServiceLogger(serviceId);
        assertSame(serviceLogger1, serviceLogger2);
        
        // 确保不同类型的Logger是不同的实例
        assertNotSame(logger1, serviceLogger1);
    }
}

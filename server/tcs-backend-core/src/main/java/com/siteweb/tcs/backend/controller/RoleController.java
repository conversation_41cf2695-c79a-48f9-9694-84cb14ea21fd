package com.siteweb.tcs.backend.controller;


import com.siteweb.tcs.common.exception.code.StandardBusinessErrorCode;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.hub.dal.entity.Role;
import com.siteweb.tcs.hub.service.RoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/role")
public class RoleController {

    @Autowired
    private RoleService roleService;

    @PostMapping(value = "/create", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> createRole(@RequestBody Role role) {
        try {
            return ResponseHelper.successful(roleService.createRole(role));
        } catch (RuntimeException e) {
            log.error("Create role failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_CREATE_ERROR, e.getMessage());
        }
    }

    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateRole(@RequestBody Role role) {
        try {
            Role updatedRole = roleService.updateRole(role);
            if (updatedRole == null) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "Role not found with ID: " + role.getRoleId());
            }
            return ResponseHelper.successful(updatedRole);
        } catch (RuntimeException e) {
            log.error("Update role failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        }
    }

    @DeleteMapping(value = "/{roleId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> deleteRole(@PathVariable Integer roleId) {
        try {
            boolean result = roleService.removeRole(roleId);
            if (!result) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "Role not found with ID: " + roleId);
            }
            return ResponseHelper.successful(true);
        } catch (RuntimeException e) {
            log.error("Delete role failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.RECORD_DELETE_ERROR, e.getMessage());
        }
    }

    @GetMapping(value = "/{roleId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRole(@PathVariable Integer roleId) {
        try {
            Role role = roleService.getById(roleId);
            if (role == null) {
                return ResponseHelper.failed(StandardBusinessErrorCode.DATA_NOT_FOUND, "Role not found with ID: " + roleId);
            }
            return ResponseHelper.successful(role);
        } catch (RuntimeException e) {
            log.error("Get role failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        }
    }

    @GetMapping(value = "/all", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getAllRoles() {
        try {
            return ResponseHelper.successful(roleService.getAllRoles());
        } catch (RuntimeException e) {
            log.error("Get all roles failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        }
    }

    @GetMapping(value = "/user/{userId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> getRolesByUserId(@PathVariable Integer userId) {
        try {
            return ResponseHelper.successful(roleService.getRolesByUserId(userId));
        } catch (RuntimeException e) {
            log.error("Get roles by userId failed", e);
            return ResponseHelper.failed(StandardBusinessErrorCode.REQUEST_PARAMETER_INVALID, e.getMessage());
        }
    }
}
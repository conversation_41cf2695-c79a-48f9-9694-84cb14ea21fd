package com.siteweb.tcs.backend.dto;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import lombok.Data;
import lombok.Getter;

import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;

/**
 * <AUTHOR> (2024-06-27)
 **/

@Data
public class FileElementDTO {
    private String name;
    private String path;
    private FileType type;
    private Long size;
    private String permissions;
    private LocalDateTime creationTime;
    private LocalDateTime lastModifiedTime;

    public void setRelativePath(Path basePath, Path path) {
        this.path = path.toString().replace(basePath.toString(), "").replace("\\", "/");
    }


    @JsonSerialize(using = FileTypeSerializer.class)
    public enum FileType {
        FILE("file"),
        DIRECTORY("dir");

        FileType(String value) {
            this.value = value;
        }

        @Getter
        private final String value;
    }

    static class FileTypeSerializer extends StdSerializer<FileType> {

        public FileTypeSerializer() {
            super(FileType.class);
        }

        @Override
        public void serialize(FileType fileType, JsonGenerator gen, SerializerProvider provider) throws IOException {
            gen.writeString(fileType.getValue());
        }
    }


}

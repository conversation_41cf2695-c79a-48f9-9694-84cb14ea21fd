package com.siteweb.tcs.backend.controller;

import com.siteweb.tcs.backend.dto.UpdateParameterRequest;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/actor-probe")
public class ActorProbeController {
//
//    private final ProbeManagerImpl probeManager;
//
//    @Autowired
//    public ActorProbeController(ProbeManagerImpl probeManager) {
//        this.probeManager = probeManager;
//    }
//
//    @GetMapping(value = "/root", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getRootActorProbe() {
//        return ResponseHelper.successful(probeManager.getRootActorProbes());
//    }
//
//
//    @GetMapping(value = "/child", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getChildByPath(@RequestParam(name = "actorPath", required = false) String actorPath) {
//        return ResponseHelper.successful(probeManager.getChildActorProbes(actorPath));
//    }
//
//    @GetMapping(value = "/probe", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getActorProbeByPath(@RequestParam(name = "actorPath", required = false) String actorPath) {
//        return ResponseHelper.successful(probeManager.getProbe(actorPath));
//    }
//
//
//    @GetMapping(value = "/probes", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getProbesByActorPath(@RequestParam(name = "actorPath", required = false) String actorPath) {
//        return ResponseHelper.successful(probeManager.getProbesByActorPath(actorPath));
//    }
//    @GetMapping(value = "/list", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getProbesList(@RequestParam Map<String, String> params) {
//        String actorPath = params.get("actorPath");
//        List<String> filter = new ArrayList<>();
//        for (String key : params.keySet()) {
//            if (key.startsWith("filter[")) {
//                filter.add(params.get(key));
//            }
//        }
//        return ResponseHelper.successful(probeManager.getProbeList(actorPath, filter));
//    }
//
//    @PostMapping(value = "/enableActorLog", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> enableActorLog(@RequestParam("actorPath") String actorPath,
//                                                         @RequestParam("enable") Boolean enable) {
//        probeManager.enableActorLogByActorPath(actorPath, enable);
//        return ResponseHelper.successful();
//    }
//
//    @PostMapping(value = "/enableBypass", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> enableBypass(@RequestParam("actorPath") String actorPath,
//                                                       @RequestParam("enable") Boolean enable) {
//        probeManager.enableBypassByActorPath(actorPath, enable);
//        return ResponseHelper.successful();
//    }
//
//    @PostMapping(value = "/enableDebugLog", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> enableDebugLog(@RequestParam("actorPath") String actorPath,
//                                                         @RequestParam("enable") Boolean enable) {
//        probeManager.enableDebugLogByActorPath(actorPath, enable);
//        return ResponseHelper.successful();
//    }
//
//    @GetMapping(value = "/logsQueueString", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getLogsQueueString(@RequestParam(name = "actorPath") String actorPath,
//                                                             @RequestParam(name = "queueName") String queueName) {
//        return ResponseHelper.successful(probeManager.getWindowLogsQueueStringContent(actorPath, queueName));
//    }
//
//    @PostMapping(value = "/batchLogsQueueString", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> getLogsQueueStrings(@RequestBody List<Map<String, String>> inputList) {
//        List<String> res = new ArrayList<>();
//
//        for (Map<String, String> item : inputList) {
//            String path = item.get("path");
//            String name = item.get("name");
//            String windowLogsQueueStringContent = probeManager.getWindowLogsQueueStringContent(path, name);
//            res.add(windowLogsQueueStringContent);
//        }
//        return ResponseHelper.successful(res);
//    }
//
//    @PostMapping(value = "/updateParameter", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> updateParameter(@RequestBody UpdateParameterRequest request) {
//        probeManager.setActorParameters(request.getActorPath(), request.getParameterName(), request.getValue());
//        return ResponseHelper.successful();
//    }
//
//    //批量控制enableActorLog
//    @PostMapping(value = "/batchEnableActorLog", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> batchEnableActorLog(@RequestParam("actorPath") String actorPath, @RequestParam("enable") Boolean enable) {
//        probeManager.batchEnableActorLog(actorPath, enable);
//        return ResponseHelper.successful();
//    }
//
//    //批量控制enableBypass
//    @PostMapping(value = "/batchEnableBypass", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> batchEnableBypass(@RequestParam("actorPath") String actorPath, @RequestParam("enable") Boolean enable) {
//        probeManager.batchEnableBypass(actorPath, enable);
//        return ResponseHelper.successful();
//    }
//
//    //批量控制enableDebugLog
//    @PostMapping(value = "/batchEnableDebugLog", produces = MediaType.APPLICATION_JSON_VALUE)
//    public ResponseEntity<ResponseResult> batchEnableDebugLog(@RequestParam("actorPath") String actorPath, @RequestParam("enable") Boolean enable) {
//        probeManager.batchEnableDebugLog(actorPath, enable);
//        return ResponseHelper.successful();
//    }

}

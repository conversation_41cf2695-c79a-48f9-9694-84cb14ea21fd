-- =====================================================
-- 基础数据初始化 - 资源类型（简化版）
-- =====================================================

-- 1. MySQL资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'MYSQL',
    'MySQL',
    'RELATIONAL_DB',
    'MySQL关系型数据库',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "tcs_middleware",
        "username": "root",
        "password": "ENC(password)",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'mysql-config'
);

-- 2. Redis资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'REDIS',
    'Redis',
    'KEY_VALUE_STORE',
    'Redis键值存储',
    '{
        "host": "localhost",
        "port": 6379,
        "password": "ENC(password)",
        "database": 0,
        "connectionTimeout": 2000,
        "maxTotal": 8,
        "maxIdle": 8,
        "minIdle": 0
    }',
    'redis-config'
);

-- 3. H2资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'H2',
    'H2 Database',
    'RELATIONAL_DB',
    'H2内存/文件数据库',
    '{
        "dbName": "testdb",
        "mode": "MEMORY",
        "filePath": "./h2db/testdb",
        "username": "sa",
        "password": "ENC(password)",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'h2-config'
);

-- 4. PostgreSQL资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'POSTGRESQL',
    'PostgreSQL',
    'RELATIONAL_DB',
    'PostgreSQL关系型数据库',
    '{
        "host": "localhost",
        "port": 5432,
        "database": "tcs_middleware",
        "username": "postgres",
        "password": "ENC(password)",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "maxLifetime": 1800000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware",
        "autoCommit": true,
        "transactionIsolation": "TRANSACTION_READ_COMMITTED"
    }',
    'postgresql-config'
);

-- 5. Kafka资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'KAFKA',
    'Kafka',
    'MESSAGE_QUEUE',
    'Kafka消息队列',
    '{
        "bootstrapServers": "localhost:9092",
        "clientId": "tcs-middleware",
        "keySerializer": "org.apache.kafka.common.serialization.StringSerializer",
        "valueSerializer": "org.apache.kafka.common.serialization.StringSerializer",
        "acks": "all",
        "retries": 3,
        "batchSize": 16384,
        "lingerMs": 1,
        "bufferMemory": 33554432
    }',
    'kafka-config'
);

-- 6. MQTT资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'MQTT',
    'MQTT(Mosquito)',
    'MESSAGE_QUEUE',
    'MQTT消息队列',
    '{
        "serverUri": "tcp://localhost:1883",
        "clientId": "tcs-middleware",
        "username": "",
        "password": "ENC(password)",
        "cleanSession": true,
        "connectionTimeout": 30,
        "keepAliveInterval": 60,
        "automaticReconnect": true
    }',
    'mos-mqtt-config'
);

-- 7. HTTP服务器资源类型
INSERT INTO mw_resource_type (id, name, category, description, default_config, ui_component)
VALUES (
    'HTTP_SERVER',
    'HTTP服务器',
    'WEB_SERVER',
    'HTTP服务器资源，基于Pekko Akka HTTP实现',
    '{
        "host": "0.0.0.0",
        "port": 8080,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'http-server-config'
);

-- =====================================================
-- 基础数据初始化 - 服务类型
-- =====================================================

-- 1. 数据库服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'DATABASE',
    '数据库服务',
    '提供数据库访问服务',
    '{}',
    'database-service-config',
    'RELATIONAL_DB'
);

-- 2. 键值存储服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'KEY_VALUE_STORE',
    '键值存储服务',
    '提供键值存储服务',
    '{
        "batchThreshold": 100,
        "timeThresholdMs": 3000
    }',
    'key-value-store-service-config',
    'KEY_VALUE_STORE'
);

-- 3. 消息队列服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'MESSAGE_QUEUE',
    '消息队列服务',
    '提供消息队列服务',
    '{
        "defaultTopic": "default-topic",
        "producerConfig": {
            "acks": "all",
            "retries": 3,
            "batchSize": 16384
        },
        "consumerConfig": {
            "autoCommit": true,
            "autoCommitInterval": 5000,
            "maxPollRecords": 500
        }
    }',
    'message-queue-service-config',
    'MESSAGE_QUEUE'
);

-- 4. Siteweb持久化服务
INSERT INTO mw_service_type (id, name, description, default_config, ui_component, supported_resource_category)
VALUES (
    'SITEWEB_PERSISTENT',
    'Siteweb持久化服务',
    '用于封装tcs-siteweb模块中的各种service，提供统一的访问接口，支持关系型数据库',
    '{}',
    'siteweb-persistent-service-config',
    'RELATIONAL_DB'
);

-- =====================================================
-- 测试数据初始化 - 资源配置
-- =====================================================
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-h2-config-001',
    'H2',
    '测试H2资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "testdb",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cucc-h2-config-primary',
    'H2',
    'cucc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_cucc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'cmcc-h2-config-primary',
    'H2',
    'cmcc H2 数据库资源',
    '用于测试的H2内存数据库资源配置',
    '{
        "dbName": "test_cmcc",
        "mode": "MEMORY",
        "username": "sa",
        "password": "",
        "maxPoolSize": 10,
        "minIdle": 1
    }',
    'ENABLED',
    'system'
);

-- 1. MySQL测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mysql-config-001',
    'MYSQL',
    '测试MySQL资源',
    '用于测试的MySQL资源配置',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "tcs_middleware",
        "username": "root",
        "password": "root",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'ENABLED',
    'system'
);

INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mysql-siteweb-001',
    'MYSQL',
    '测试MySQL资源',
    '用于测试的MySQL资源配置',
    '{
        "host": "localhost",
        "port": 3306,
        "database": "siteweb_bytedance",
        "username": "root",
        "password": "root",
        "minIdle": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000
    }',
    'ENABLED',
    'system'
);

-- 2. PostgreSQL测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-postgresql-config-001',
    'POSTGRESQL',
    '测试PostgreSQL资源',
    '用于测试的PostgreSQL资源配置',
    '{
        "host": "*************",
        "port": 5432,
        "database": "postgres",
        "username": "postgres",
        "password": "Vertiv@086",
        "schema": "public",
        "minPoolSize": 5,
        "maxPoolSize": 20,
        "connectionTimeout": 30000,
        "idleTimeout": 600000,
        "sslEnabled": false,
        "sslMode": "prefer",
        "applicationName": "TCS-Middleware-Test"
    }',
    'ENABLED',
    'system'
);

-- 3. Redis测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-redis-config-001',
    'REDIS',
    '测试Redis资源',
    '用于测试的Redis资源配置',
    '{
        "host": "**************",
        "port": 6379,
        "password": "siteweb1!",
        "database": 0,
        "connectionTimeout": 2000,
        "maxTotal": 8,
        "maxIdle": 8,
        "minIdle": 0
    }',
    'ENABLED',
    'system'
);

-- 4. MQTT测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-mqtt-config-001',
    'MQTT',
    '测试MQTT资源',
    '用于测试的MQTT资源配置',
    '{
        "serverUri": "tcp://*************:1883",
        "clientId": "tcs-middleware-test",
        "username": "",
        "password": "",
        "cleanSession": true,
        "connectionTimeout": 30,
        "keepAliveInterval": 60,
        "automaticReconnect": true
    }',
    'ENABLED',
    'system'
);

-- 5. HTTP服务器测试资源配置
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'test-http-server-config-001',
    'HTTP_SERVER',
    '测试HTTP服务器',
    '用于测试的HTTP服务器资源配置',
    '{
        "host": "localhost",
        "port": 8088,
        "idleTimeout": 60,
        "backlog": 100
    }',
    'ENABLED',
    'system'
);

-- =====================================================
-- 测试数据初始化 - 服务配置
-- =====================================================

-- 1. 数据库服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-db-service-001',
    'DATABASE',
    '测试数据库服务',
    '用于测试的数据库服务配置',
    '{}',
    'test-postgresql-config-001',
    'ENABLED',
    'system'
);

-- 2. 键值存储服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-kvs-service-001',
    'KEY_VALUE_STORE',
    '测试键值存储服务',
    '用于测试的键值存储服务配置，包含批处理优化',
    '{
        "defaultTTL": 3600,
        "keyPrefix": "test:",
        "serializationFormat": "JSON",
        "batchThreshold": 50,
        "timeThresholdMs": 2000,
        "resourceId": "test-redis-config-001"
    }',
    'test-redis-config-001',
    'ENABLED',
    'system'
);

-- 3. 消息队列服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-mq-service-001',
    'MESSAGE_QUEUE',
    '测试消息队列服务',
    '用于测试的消息队列服务配置',
    '{
        "defaultTopic": "test-topic",
        "producerConfig": {
            "acks": "all",
            "retries": 3,
            "batchSize": 16384
        },
        "consumerConfig": {
            "autoCommit": true,
            "autoCommitInterval": 5000,
            "maxPollRecords": 500
        }
    }',
    'test-mqtt-config-001',
    'ENABLED',
    'system'
);
-- =====================================================
-- H2文件类型存储配置初始化
-- =====================================================

-- H2文件类型存储配置 - 基础版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-config-001',
    'H2',
    'H2文件数据库资源',
    '用于持久化存储的H2文件数据库配置',
    '{
        "dbName": "tcs_file_db",
        "mode": "FILE",
        "filePath": "./data/h2db/tcs_file_db",
        "username": "sa",
        "password": "",
        "compatibilityMode": "MYSQL",
        "autoServerMode": false,
        "autoReconnect": true,
        "maxPoolSize": 15,
        "minIdle": 2,
        "idleTimeout": 30000,
        "connectionTimeout": 30000,
        "maxLifetime": 1800000,
        "autoCommit": true
    }',
    'ENABLED',
    'system'
);

-- H2文件类型存储配置 - 生产环境版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-prod-config-001',
    'H2',
    '生产H2文件数据库',
    '生产环境使用的H2文件数据库配置，具有更大的连接池和更长的超时时间',
    '{
        "dbName": "tcs_prod_db",
        "mode": "FILE",
        "filePath": "./data/h2db/production/tcs_prod_db",
        "username": "sa",
        "password": "ENC(prod_password)",
        "compatibilityMode": "MYSQL",
        "autoServerMode": true,
        "autoReconnect": true,
        "maxPoolSize": 30,
        "minIdle": 5,
        "idleTimeout": 60000,
        "connectionTimeout": 30000,
        "maxLifetime": 3600000,
        "autoCommit": true
    }',
    'ENABLED',
    'system'
);

-- H2文件类型存储配置 - 开发环境版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-dev-config-001',
    'H2',
    '开发H2文件数据库',
    '开发环境使用的H2文件数据库配置，启用H2控制台访问',
    '{
        "dbName": "tcs_dev_db",
        "mode": "FILE",
        "filePath": "./data/h2db/development/tcs_dev_db",
        "username": "sa",
        "password": "",
        "compatibilityMode": "MYSQL",
        "autoServerMode": true,
        "autoReconnect": true,
        "maxPoolSize": 10,
        "minIdle": 1,
        "idleTimeout": 30000,
        "connectionTimeout": 30000,
        "maxLifetime": 1800000,
        "autoCommit": true
    }',
    'ENABLED',
    'system'
);

-- H2文件类型存储配置 - 备份数据库版本
INSERT INTO mw_resource_configuration (id, resource_id, name, description, config, status, created_by)
VALUES (
    'h2-file-backup-config-001',
    'H2',
    '备份H2文件数据库',
    '用于数据备份的H2文件数据库配置',
    '{
        "dbName": "tcs_backup_db",
        "mode": "FILE",
        "filePath": "./data/h2db/backup/tcs_backup_db",
        "username": "sa",
        "password": "ENC(backup_password)",
        "compatibilityMode": "MYSQL",
        "autoServerMode": false,
        "autoReconnect": true,
        "maxPoolSize": 5,
        "minIdle": 1,
        "idleTimeout": 30000,
        "connectionTimeout": 30000,
        "maxLifetime": 1800000,
        "autoCommit": true
    }',
    'DISABLED',
    'system'
);

-- =====================================================
-- H2文件配置初始化完成
-- =====================================================

-- 4. Siteweb持久化服务测试配置
INSERT INTO mw_service_configuration (id, service_id, name, description, config, resource_configuration_id, status, created_by)
VALUES (
    'test-siteweb-persistent-service-001',
    'SITEWEB_PERSISTENT',
    '测试Siteweb持久化服务',
    '用于测试的Siteweb持久化服务配置，封装tcs-siteweb模块的服务，使用H2数据库',
    '{}',
    'test-mysql-siteweb-001',
    'ENABLED',
    'system'
);


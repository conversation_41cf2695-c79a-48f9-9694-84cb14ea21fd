-- =====================================================
-- 表结构定义
-- =====================================================

-- 创建资源类型表
CREATE TABLE IF NOT EXISTS mw_resource_type (
    id VARCHAR(50) PRIMARY KEY COMMENT '资源类型唯一标识 (如 "MYSQL", "KAFKA")',
    name VARCHAR(100) NOT NULL COMMENT '资源类型名称 (如 "MySQL", "Kafka")',
    category VARCHAR(50) NOT NULL COMMENT '资源类别（如 RELATIONAL_DB, MESSAGE_QUEUE）',
    description TEXT COMMENT '资源类型描述',
    default_config JSON NOT NULL COMMENT '配置模板（JSON），定义了创建该类型资源配置所需的参数结构',
    ui_component VARCHAR(50) NOT NULL COMMENT '前端页面资源配置组件',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源类型表';

-- 创建资源配置表
CREATE TABLE IF NOT EXISTS mw_resource_configuration (
    id VARCHAR(50) PRIMARY KEY COMMENT '资源配置唯一标识 (UUID 或其他全局唯一 ID)',
    resource_id VARCHAR(50) NOT NULL COMMENT '关联的资源类型ID',
    name VARCHAR(100) NOT NULL COMMENT '资源配置名称（如"生产主库"，"测试Redis"）',
    description TEXT COMMENT '资源配置描述',
    config JSON NOT NULL COMMENT '资源配置详情（JSON），存储了具体的连接参数、账号密码等',
    status VARCHAR(20) NOT NULL DEFAULT 'DISABLED' COMMENT '资源配置的管理状态（ENABLED/DISABLED/PENDING）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    FOREIGN KEY (resource_id) REFERENCES mw_resource_type(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='资源配置表';

-- 创建服务类型表
CREATE TABLE IF NOT EXISTS mw_service_type (
    id VARCHAR(50) PRIMARY KEY COMMENT '服务类型唯一标识 (如 "DATABASE_SERVICE", "CACHE_SERVICE")',
    name VARCHAR(100) NOT NULL COMMENT '服务类型名称 (如 "Database Service", "Cache Service")',
    description TEXT COMMENT '服务类型描述',
    default_config JSON COMMENT '配置模板（JSON），定义服务层面的配置参数',
    ui_component VARCHAR(50) NOT NULL COMMENT '前端页面资源配置组件',
    supported_resource_category VARCHAR(50) NOT NULL COMMENT '支持的资源类别（如 RELATIONAL_DB, KEY_VALUE）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务类型表';

-- 创建服务配置表
CREATE TABLE IF NOT EXISTS mw_service_configuration (
    id VARCHAR(50) PRIMARY KEY COMMENT '服务配置唯一标识',
    service_id VARCHAR(50) NOT NULL COMMENT '关联的服务类型ID',
    name VARCHAR(100) NOT NULL COMMENT '服务配置名称（如"主库服务"，"用户缓存服务"）',
    description TEXT COMMENT '服务配置描述',
    config JSON COMMENT '服务配置详情（JSON），存储服务实例特有的参数',
    resource_configuration_id VARCHAR(50) COMMENT '关联的资源配置ID',
    status VARCHAR(20) NOT NULL DEFAULT 'DISABLED' COMMENT '服务配置的管理状态（ENABLED/DISABLED/PENDING）',
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    created_by VARCHAR(50) COMMENT '创建者',
    updated_by VARCHAR(50) COMMENT '更新者',
    FOREIGN KEY (service_id) REFERENCES mw_service_type(id),
    FOREIGN KEY (resource_configuration_id) REFERENCES mw_resource_configuration(id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='服务配置表';

-- =====================================================
-- 索引创建
-- =====================================================

-- 创建资源配置表索引
CREATE INDEX idx_resource_configuration_resource_id ON mw_resource_configuration(resource_id);
CREATE INDEX idx_service_configuration_service_id ON mw_service_configuration(service_id);
CREATE INDEX idx_service_configuration_resource_configuration_id ON mw_service_configuration(resource_configuration_id);

CREATE TABLE `tcs_streams` (
  `streamGraphId` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '实例化图id',
  `streamGraphName` VARCHAR(255) COMMENT '实例化图名称',
  `graphOption` JSON COMMENT '实例化图配置（JSON格式）',
  `flows` JSON COMMENT '实例化流列表（JSON格式）'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='实例化图表';

CREATE TABLE `tcs_stream_modules` (
  `moduleId` VARCHAR(64) PRIMARY KEY COMMENT '模块唯一ID',
  `moduleName` VARCHAR(255) COMMENT '模块名称',
  `moduleVersion` VARCHAR(64) COMMENT '模块版本',
  `moduleProvider` VARCHAR(255) COMMENT '模块支持',
  `jarFile` VARCHAR(1024) COMMENT 'Jar包路径',
  `jarCode` VARCHAR(64) COMMENT 'Jar包code',
  `enable` BOOLEAN DEFAULT TRUE COMMENT '是否启用',
  `createTime` DATETIME COMMENT '创建时间',
  `updateTime` DATETIME COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='模块定义表';


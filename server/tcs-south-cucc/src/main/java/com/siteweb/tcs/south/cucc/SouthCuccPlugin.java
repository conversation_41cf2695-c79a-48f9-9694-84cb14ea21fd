package com.siteweb.tcs.south.cucc;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.runtime.SouthPlugin;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.south.cucc.connector.ConnectorDataHolder;
import com.siteweb.tcs.south.cucc.connector.process.CuccGuard;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.MessageSource;

import java.util.concurrent.TimeoutException;

/**
 * 中国联通南向接入插件
 * <p>
 * 此插件用于接入中国联通平台数据
 * </p>
 */
@Slf4j
public class SouthCuccPlugin extends SouthPlugin {

    @Autowired
    private ConnectorDataHolder dataHolder;

    @Value("${plugin.middleware.database.primary}")
    private String dbResourceId;

    @Autowired
    private MessageSource messageSource;

    @Autowired
    private ResourceRegistry resourceRegistry;

    public SouthCuccPlugin(PluginContext context) {
        super(context);
    }

    @Override
    public void onStart() {
        try {
            log.info("Starting SouthCuccPlugin");

            // Set up the main plugin actor
            dataHolder.setPluginId(this.getPluginId());
            ActorRef selfRootActor = null;
            dataHolder.setRootActor(selfRootActor);

            // Initialize other components here

            log.info("SouthCuccPlugin started successfully");
        } catch (Exception e) {
            log.error("Error starting SouthCuccPlugin", e);
        }
    }

    @Override
    public void onStop() {
        log.info("Stopping SouthCuccPlugin");

        // 移除资源引用，避免影响其他插件
        try {
            boolean canDestroy = resourceRegistry.removeResourceReference(dbResourceId, "south-cucc-plugin");
            if (canDestroy) {
                log.info("Resource {} has no more references, it can be safely destroyed", dbResourceId);
            } else {
                log.info("Resource {} still has other references: {}",
                        dbResourceId, resourceRegistry.getResourceReferences(dbResourceId));
            }
        } catch (Exception e) {
            log.warn("Failed to remove resource reference for {}: {}", dbResourceId, e.getMessage());
        }

        // The actor system will handle stopping the actors
    }

    /**
     * Creates a child actor for this plugin.
     *
     * @param props the Props for the actor
     * @param name the name of the actor
     * @return the ActorRef for the created actor
     * @throws TimeoutException if the actor creation times out
     */
    private ActorRef createChildActor(Props props, String name) throws TimeoutException, InterruptedException {
        return actorSystem.actorOf(props, getPluginId() + "-" + name);
    }
} 
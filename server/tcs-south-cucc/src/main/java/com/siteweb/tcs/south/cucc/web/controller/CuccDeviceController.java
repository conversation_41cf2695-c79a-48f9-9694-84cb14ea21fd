package com.siteweb.tcs.south.cucc.web.controller;

import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import com.siteweb.tcs.siteweb.entity.House;
import com.siteweb.tcs.south.cucc.dal.entity.CuccDevice;
import com.siteweb.tcs.south.cucc.web.service.CuccDeviceService;
import com.siteweb.tcs.south.cucc.web.vo.CuccDeviceVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 联通设备Controller
 */
@Slf4j
@RestController
@RequestMapping("/devices")
public class CuccDeviceController {

    @Autowired
    private CuccDeviceService deviceService;
    @Autowired
    private ServiceRegistry serviceRegistry;

    /**
     * 获取设备列表
     */
    @GetMapping(value = "/test")
    public ResponseEntity<List<CuccDeviceVO>> listDevices(@RequestParam(required = false) Integer deviceType) {
        List<CuccDeviceVO> devices;
        if (deviceType != null) {
            devices = deviceService.listDevicesByType(deviceType);
        } else {
            devices = deviceService.listAllDevices();
        }
        return ResponseEntity.ok(devices);
    }

    @GetMapping(value = "/testhouse")
    public ResponseEntity<List<House>> listhouses() {
        SitewebPersistentService sitewebPersistentService = (SitewebPersistentService) serviceRegistry.get("test-siteweb-persistent-service-001");
        List<House> houses = sitewebPersistentService.list(House.class);
        return ResponseEntity.ok(houses);
    }

    /**
     * 获取在线设备
     */
    @GetMapping("/online")
    public ResponseEntity<List<CuccDeviceVO>> listOnlineDevices() {
        return ResponseEntity.ok(deviceService.listOnlineDevices());
    }

    /**
     * 获取设备详情
     */
    @GetMapping("/{deviceId}")
    public ResponseEntity<CuccDeviceVO> getDevice(@PathVariable Long deviceId) {
        CuccDeviceVO device = deviceService.getDeviceById(deviceId);
        if (device != null) {
            return ResponseEntity.ok(device);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 创建设备
     */
    @PostMapping(value = "/test")
    public ResponseEntity<CuccDeviceVO> createDevice(@RequestBody CuccDevice device) {
        CuccDeviceVO createdDevice = deviceService.createDevice(device);
        return ResponseEntity.ok(createdDevice);
    }

    /**
     * 更新设备
     */
    @PutMapping("/{deviceId}")
    public ResponseEntity<CuccDeviceVO> updateDevice(@PathVariable Long deviceId, @RequestBody CuccDevice device) {
        device.setDeviceId(deviceId);
        CuccDeviceVO updatedDevice = deviceService.updateDevice(device);
        if (updatedDevice != null) {
            return ResponseEntity.ok(updatedDevice);
        } else {
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 删除设备
     */
    @DeleteMapping("/{deviceId}")
    public ResponseEntity<Void> deleteDevice(@PathVariable Long deviceId) {
        boolean success = deviceService.deleteDevice(deviceId);
        if (success) {
            return ResponseEntity.noContent().build();
        } else {
            return ResponseEntity.notFound().build();
        }
    }
} 
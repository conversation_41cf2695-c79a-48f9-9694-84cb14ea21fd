package com.siteweb.stream.service.configure;

import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorSystem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * Stream Actor System 验证器
 * 在应用启动时验证 Stream 模块是否正确使用 TCS 的 Actor System
 */
@Component
@Slf4j
public class StreamActorSystemValidator implements ApplicationListener<ApplicationStartedEvent> {

//    @Autowired
//    private ActorSystem actorSystem;

    @Override
    public void onApplicationEvent(ApplicationStartedEvent event) {
//        log.info("=== Stream Actor System 验证器启动 ===");
//
//        try {
//            // 验证 Actor System 是否正确注入
//            if (actorSystem != null) {
//                log.info("✓ Actor System 成功注入");
//                log.info("  - Actor System 名称: {}", actorSystem.name());
//                log.info("  - Actor System 地址: {}", actorSystem.provider().getDefaultAddress());
//
//                // 验证 Pekko 版本
//                try {
//                    String pekkoVersion = actorSystem.settings().config().getString("pekko.version");
//                    log.info("  - 使用的 Pekko 版本: {}", pekkoVersion);
//                } catch (Exception e) {
//                    log.warn("  - 无法获取 Pekko 版本: {}", e.getMessage());
//                }
//
//                // 验证集群配置
//                try {
//                    boolean isClusterEnabled = actorSystem.settings().config().hasPath("pekko.cluster");
//                    log.info("  - 集群模式启用: {}", isClusterEnabled);
//
//                    if (isClusterEnabled) {
//                        var seedNodes = actorSystem.settings().config().getStringList("pekko.cluster.seed-nodes");
//                        log.info("  - 种子节点: {}", seedNodes);
//                    }
//                } catch (Exception e) {
//                    log.warn("  - 无法获取集群配置: {}", e.getMessage());
//                }
//
//                // 验证分片配置
//                try {
//                    boolean hasSharding = actorSystem.settings().config().hasPath("pekko.cluster.sharding");
//                    log.info("  - 分片功能配置: {}", hasSharding);
//                } catch (Exception e) {
//                    log.warn("  - 无法获取分片配置: {}", e.getMessage());
//                }
//
//                log.info("✓ Stream 模块 Actor System 验证完成");
//
//            } else {
//                log.error("✗ Actor System 注入失败 - 为 null");
//            }
//
//        } catch (Exception e) {
//            log.error("✗ Stream Actor System 验证过程中发生异常", e);
//        }
//
//        log.info("=== Stream Actor System 验证器完成 ===");
    }
}

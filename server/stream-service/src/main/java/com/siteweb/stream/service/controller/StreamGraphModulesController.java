package com.siteweb.stream.service.controller;


import com.siteweb.stream.common.stream.StreamPluginInfo;
import com.siteweb.stream.core.entity.StreamModule;
import com.siteweb.stream.service.service.StreamModuleService;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/stream-modules")
public class StreamGraphModulesController {
    private static final Logger logger = LoggerFactory.getLogger(StreamGraphModulesController.class);
    @Autowired
    private StreamModuleService streamModuleService;


    @PostMapping("/upload")
    public Boolean uploadModule(@RequestParam("file") MultipartFile file) {
        try {
            streamModuleService.uploadModule(file);
            logger.info("Stream Module[{}] loaded successfully.", file.getName());
            return true;
        } catch (Exception e) {
            logger.error("Stream Module[{}] loaded Failed.", file.getName(), e);
            return false;
        }
    }

    @PutMapping("/unload")
    public Boolean unloadModule(@RequestParam String moduleId) {
        try {
            streamModuleService.unloadModule(moduleId);
            logger.info("The unloading of the Stream Module[{}] successfully.", moduleId);
            return true;
        } catch (Exception e) {
            logger.error("The unloading of the Stream Module[{}] failed.", moduleId, e);
            return false;
        }
    }


    @PutMapping("/enable")
    public Boolean enableModule(@RequestParam String moduleId) {
        try {
            streamModuleService.enableModule(moduleId);
            logger.info("The unloading of the Stream Module[{}] successfully.", moduleId);
            return true;
        } catch (Exception e) {
            logger.error("The unloading of the Stream Module[{}] failed.", moduleId, e);
            return false;
        }
    }

    @PutMapping("/disable")
    public Boolean disableModule(@RequestParam String moduleId) {
        try {
            streamModuleService.disableModule(moduleId);
            logger.info("The unloading of the Stream Module[{}] successfully.", moduleId);
            return true;
        } catch (Exception e) {
            logger.error("The unloading of the Stream Module[{}] failed.", moduleId, e);
            return false;
        }
    }





    @GetMapping("/getAllPluginInfos")
    public List<StreamModule> getAllPluginInfos() {
        return streamModuleService.getModules();
    }
//
}

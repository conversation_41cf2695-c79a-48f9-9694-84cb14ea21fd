package com.siteweb.stream.service.controller;

import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.stream.core.manager.StreamGraphInstanceManager;
import com.siteweb.stream.service.service.StreamGraphService;
import com.siteweb.tcs.common.response.ResponseHelper;
import com.siteweb.tcs.common.response.ResponseResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/streamGraph")
public class StreamGraphController {
    private static final Logger logger = LoggerFactory.getLogger(StreamGraphController.class);

    @Autowired
    private StreamGraphService streamGraphService;


    @GetMapping(value = "/{graphId}", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> findByGraphId(@PathVariable("graphId") Long graphId) throws IOException {
        return ResponseHelper.successful(streamGraphService.findGraph(graphId));
    }

    @PutMapping(value = "/update", produces = MediaType.APPLICATION_JSON_VALUE)
    public ResponseEntity<ResponseResult> updateGraph(@RequestBody StreamGraph streamGraph) {
        try {
            if (Objects.isNull(streamGraph) || Objects.isNull(streamGraph.getStreamGraphName()) || streamGraph.getFlows() == null) {
                return ResponseHelper.failed(-1, "无效的数据");
            }
            streamGraphService.updateGraph(streamGraph);
            if (StreamGraphInstanceManager.getInstance().graphInstanceExist(streamGraph.getStreamGraphId())) {
                StreamGraphInstanceManager.getInstance().destroyGraph(streamGraph.getStreamGraphId());
            }
            // TODO 上下文只能从Actor内部获取 具体创建逻辑需在Actor内部进行
            ActorContext context = null;
            StreamGraphInstanceManager.getInstance().createGraph(context, streamGraph, null);
            StreamGraphInstanceManager.getInstance().startGraph(streamGraph.getStreamGraphId());
            return ResponseHelper.successful();
        } catch (Exception e) {
            logger.error("Failed to update graph", e);
            return ResponseHelper.failed(-1, e.getMessage());
        }
    }


}

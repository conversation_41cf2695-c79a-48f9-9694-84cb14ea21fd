package com.siteweb.stream.service.service;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.stream.common.exception.StreamException;
import com.siteweb.stream.common.exception.StreamPluginException;
import com.siteweb.stream.common.runtime.EnumDescriptor;
import com.siteweb.stream.common.stream.StreamShapeInfo;
import com.siteweb.stream.core.entity.StreamModule;
import com.siteweb.stream.core.manager.StreamModuleManager;
import com.siteweb.stream.core.mapper.StreamModuleMapper;
import io.jsonwebtoken.lang.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.jar.Attributes;
import java.util.jar.JarFile;
import java.util.jar.Manifest;

/**
 * <AUTHOR> (2025-05-27)
 **/
@Slf4j
@Service
public class StreamModuleService {
    private static final String STREAM_MODULE_ID = "stream.module.id";
    private static final String STREAM_MODULE_NAME = "stream.module.name";
    private static final String STREAM_MODULE_PROVIDER = "stream.module.provider";
    private static final String STREAM_MODULE_VERSION = "stream.module.version";
    @Autowired
    private StreamModuleMapper streamModuleMapper;


    private final StreamModuleManager streamModuleManager = StreamModuleManager.INSTANCE;

    public void uploadModule(MultipartFile file) throws Exception {
        File tempFile = File.createTempFile("tcs", ".tmp");
        tempFile.deleteOnExit();
        file.transferTo(tempFile);
        // 先读Module信息
        StreamModule streamModule = parseJarModule(tempFile);
        tempFile.delete();
        // 拷贝
        var fileName = String.format("stream-modules/%s.%s.jar", streamModule.getModuleId(), streamModule.getModuleVersion());
        File storeJarFile = new File(fileName);
        if (storeJarFile.exists()) {
            throw new Exception(String.format("The graph module “%s” already exists", streamModule.getModuleId()));
        }
        file.transferTo(storeJarFile);
        streamModule.setJarFile(fileName);
        streamModule.setJarCode(StreamModuleManager.fileSha256(fileName));
        // 持久化
        streamModuleMapper.insert(streamModule);
        // 加载
        streamModuleManager.loadStreamModule(streamModule);
    }


    public StreamModule parseJarModule(File jarFile) {
        var gm = new StreamModule();
        try (JarFile jar = new JarFile(jarFile)) {
            Manifest manifest = jar.getManifest();
            Attributes attributes = manifest.getMainAttributes();
            gm.setModuleId(attributes.getValue(STREAM_MODULE_ID));
            gm.setModuleName(attributes.getValue(STREAM_MODULE_NAME));
            gm.setModuleVersion(attributes.getValue(STREAM_MODULE_VERSION));
            gm.setModuleProvider(attributes.getValue(STREAM_MODULE_PROVIDER));
            if (Objects.isEmpty(gm.getModuleId())){
                throw new Exception("Invalid graph module jar file.");
            }
            gm.setJarFile("");
            gm.setJarCode("");
            gm.setEnable(true);
            gm.setCreateTime(LocalDateTime.now());
            gm.setUpdateTime(LocalDateTime.now());
            return gm;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


    public void unloadModule(String moduleId) {
        if (streamModuleManager.unloadStreamLibrary(moduleId)) {
            streamModuleMapper.deleteById(moduleId);
            // 建议 GC（不能保证立即回收，但通常有效）
            System.gc();
        }
    }


    public void enableModule(String moduleId) throws Exception {
        var module= streamModuleMapper.selectById(moduleId);
        if (module == null)  throw StreamException.STREAM_MODULE_NOT_FOUND.toException();
        if (streamModuleManager.moduleIsLoaded(moduleId)){
            throw StreamException.STREAM_MODULE_HAS_BEEN_LOADED.toException();
        }
        module.setEnable(true);
        streamModuleMapper.updateById(module);
        streamModuleManager.loadStreamModule(module);
    }

    public void disableModule(String moduleId) {
        var module= streamModuleMapper.selectById(moduleId);
        if (module == null)  throw StreamException.STREAM_MODULE_NOT_FOUND.toException();
        if (module.getEnable()){
            module.setEnable(false);
            streamModuleMapper.updateById(module);
        }
        if (streamModuleManager.unloadStreamLibrary(moduleId)) {
            System.gc();
        }
    }

    public List<StreamShapeInfo> getShapes(String i18n) {
        return streamModuleManager.getShapes(i18n);
    }

    public List<EnumDescriptor> getEnums(String i18n) {
        return streamModuleManager.getEnums(i18n);
    }

    public List<StreamModule> getModules() {
        return streamModuleMapper.selectList(Wrappers.emptyWrapper());
    }


    public void loadModules() throws MalformedURLException {
        var modules = getModules();
        for (StreamModule module : modules) {
            if (module.getEnable()) {
                var jarFile = new File(module.getJarFile());
                if (!jarFile.exists()) {
                    log.error("The jar package file “{}” of the graph module does not exist.", module.getJarFile());
                }
                try {
                    streamModuleManager.loadStreamModule(module);
                } catch (Throwable throwable) {
                    log.error("Failed to load the graph module[{}].", module.getModuleId());
                }
            }
        }
    }


}

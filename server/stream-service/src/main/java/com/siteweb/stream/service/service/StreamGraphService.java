package com.siteweb.stream.service.service;


import com.fasterxml.jackson.databind.ObjectMapper;
import com.siteweb.stream.core.entity.StreamGraph;
import com.siteweb.stream.core.provider.StreamGraphProvider;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;

@Service
public class StreamGraphService {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private StreamGraphProvider streamGraphProvider;

    public void updateGraph(StreamGraph streamGraph) throws IOException {
        // 模拟更新配置
        objectMapper.writeValue(new File("temp/stream-graph/" + streamGraph.getStreamGraphId() + ".json"), streamGraph);
    }

    public StreamGraph findGraph(Long streamGraphId) throws IOException {
//        模拟读取配置
        StreamGraph person = objectMapper.readValue(new File("temp/stream-graph/" + streamGraphId + ".json"), StreamGraph.class);
        return person;
    }


}

package com.siteweb.stream.south.cmcc.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.cmcc.common.message.MobileBRequestMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_Type;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <AUTHOR> (2025-05-09)
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class GetDataMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info()      ;

    public GetDataMessage() {
        super(PK_TypeName.GET_DATA);
    }


    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;


        /**
         * 请求设备列表
         */
        @JacksonXmlElementWrapper(localName = "DeviceList")
        @JacksonXmlProperty(localName = "Device")
        private List<Device> devices;

    }


    public static class Device {
        /**
         * 设备ID。
         * 当为空，则返回该FSU所监控的所有设备的监控点的值；这种情况下，忽略IDs参数（即监控点ID列表）。
         */
        @JacksonXmlProperty(localName = "ID", isAttribute = true)
        @JsonProperty("ID")
        private String deviceID;


        /**
         * 相应的监控点ID号。
         * 当为空，则返回该设备的所有监控点的值。
         */
        @JsonProperty("ID")
        @JacksonXmlProperty(localName = "ID")
        @JacksonXmlElementWrapper(useWrapping = false)
        private List<String> points;
    }


}

package com.siteweb.stream.south.cmcc.messages;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlElementWrapper;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;

import com.siteweb.tcs.cmcc.common.protocol.TSemaphore;
import com.siteweb.tcs.cmcc.common.message.MobileBRequestMessage;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.Setter;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> (2025-05-12)
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@JacksonXmlRootElement(localName = "Request")
public class SendDataMessage extends MobileBRequestMessage {

    @JsonProperty("Info")
    @JacksonXmlProperty(localName = "Info")
    private Info info = new Info();

    public SendDataMessage() {
        super(PK_TypeName.SEND_DATA);
    }


    @Setter
    @Getter
    public static class Info {
        /**
         * FSU ID号
         */
        @JsonProperty("FSUID")
        @JacksonXmlProperty(localName = "FSUID")
        private String fsuId;


        @JacksonXmlProperty(localName = "Values")
        @JsonProperty("Values")
        private Values values;

    }


    @Setter
    @Getter
    public static class Values {
        @JacksonXmlElementWrapper(localName = "DeviceList")
        @JacksonXmlProperty(localName = "Device")
        @JsonProperty("Device")
        private List<Device> deviceList;

        public Values() {
            deviceList = new ArrayList<Device>();
        }
    }

    @Setter
    @Getter
    public static class Device {
        @JsonProperty("ID")
        @JacksonXmlProperty(localName = "ID", isAttribute = true)
        private String id;

        @JsonProperty("TSemaphore")
        @JacksonXmlProperty(localName = "TSemaphore")
        @JacksonXmlElementWrapper(useWrapping = false)
        private List<TSemaphore> signal;

        public Device() {
            signal = new ArrayList<TSemaphore>();
        }
    }


}

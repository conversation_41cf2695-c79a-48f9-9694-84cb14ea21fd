# 资源配置启用/禁用功能API文档

## 概述

为ResourceConfigController新增了启用和禁用功能，参考ServiceConfigController的实现模式，提供了对资源配置状态的管理能力。

## 新增接口

### 1. 启用资源配置

**接口地址：** `POST /middleware/resource-configs/enable/{id}`

**描述：** 启用指定的资源配置

**路径参数：**
- `id` (String): 资源配置ID

**响应格式：** `application/json`

**示例请求：**
```bash
POST /middleware/resource-configs/enable/test-resource-001
```

**成功响应：**
```json
{
  "success": true,
  "message": "success",
  "data": true
}
```

**失败响应：**
```json
{
  "success": false,
  "message": "资源配置未找到",
  "data": null
}
```

### 2. 禁用资源配置

**接口地址：** `POST /middleware/resource-configs/disable/{id}`

**描述：** 禁用指定的资源配置

**路径参数：**
- `id` (String): 资源配置ID

**响应格式：** `application/json`

**示例请求：**
```bash
POST /middleware/resource-configs/disable/test-resource-001
```

**成功响应：**
```json
{
  "success": true,
  "message": "success",
  "data": true
}
```

**失败响应：**
```json
{
  "success": false,
  "message": "更新资源配置失败",
  "data": null
}
```

## 状态说明

### 资源配置状态枚举

- **ENABLED**: 已启用状态，资源配置可以被使用
- **DISABLED**: 已禁用状态，资源配置不可使用（默认状态）
- **PENDING**: 待处理状态（可选，用于异步处理场景）

### 状态转换规则

```
DISABLED → ENABLED  (通过enable接口)
ENABLED → DISABLED  (通过disable接口)
```

## 实现特性

### 1. 国际化支持

使用了NamespacedMessageSource进行国际化消息处理：

```java
@Autowired
@Qualifier("middlewareMessageSource")
private NamespacedMessageSource messageSource;
```

支持的消息键：
- `middleware.resource.config.not_found` - 资源配置未找到
- `middleware.resource.config.update.failed` - 更新资源配置失败

### 2. 错误处理

- **资源不存在**: 返回404相关错误信息
- **更新失败**: 返回500相关错误信息
- **参数验证**: 自动验证路径参数

### 3. 事务支持

更新操作使用了ResourceConfigurationService的事务支持，确保数据一致性。

### 4. 审计日志

通过ResourceConfigurationEntity的updateTime字段自动记录更新时间。

## 使用示例

### 批量启用资源配置

```bash
# 获取所有禁用的资源配置
curl -X GET "http://localhost:8080/middleware/resource-configs/page?status=DISABLED"

# 逐个启用
curl -X POST "http://localhost:8080/middleware/resource-configs/enable/resource-001"
curl -X POST "http://localhost:8080/middleware/resource-configs/enable/resource-002"
```

### 状态查询

```bash
# 查询特定状态的资源配置
curl -X GET "http://localhost:8080/middleware/resource-configs/page?status=ENABLED&size=20"

# 查看单个资源配置状态
curl -X GET "http://localhost:8080/middleware/resource-configs/resource-001"
```

### 错误处理示例

```bash
# 尝试启用不存在的资源配置
curl -X POST "http://localhost:8080/middleware/resource-configs/enable/non-existent"

# 响应示例
{
  "success": false,
  "message": "资源配置未找到",
  "data": null
}
```

## 与ServiceConfigController的一致性

新增的功能与ServiceConfigController保持了一致的设计模式：

1. **接口路径模式**: `/enable/{id}` 和 `/disable/{id}`
2. **HTTP方法**: 使用POST方法进行状态变更
3. **响应格式**: 统一的ResponseResult包装
4. **错误处理**: 相同的错误处理逻辑
5. **国际化**: 使用相同的消息源机制

## 注意事项

1. **状态持久化**: 状态变更会立即持久化到数据库
2. **并发安全**: 使用数据库事务确保并发安全
3. **权限控制**: 建议在生产环境中添加适当的权限控制
4. **日志记录**: 所有状态变更都会记录更新时间
5. **依赖检查**: 禁用资源配置前应检查是否有服务配置依赖

## 扩展建议

1. **批量操作**: 可以考虑添加批量启用/禁用接口
2. **状态历史**: 可以添加状态变更历史记录
3. **依赖检查**: 禁用前检查服务配置依赖关系
4. **异步处理**: 对于复杂的启用/禁用操作，可以考虑异步处理

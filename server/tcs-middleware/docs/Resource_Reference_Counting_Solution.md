# Resource引用计数解决方案

## 问题背景

当多个插件共享同一个Resource（如DataSource）时，如果一个插件停止，会导致Resource被销毁，影响其他仍在运行的插件。

### 具体问题场景：
```
插件A和插件B都使用 cucc-h2-config-primary 资源
↓
插件A停止 → 触发H2Resource.doStop() → DataSource关闭
↓
插件B仍在运行，但DataSource已被关闭 → 出现错误
```

## 解决方案：Resource引用计数机制

### 1. 核心思想

为每个Resource维护引用计数，只有当引用计数为0时才真正销毁Resource。

### 2. 实现架构

#### ResourceRegistry增强
```java
// 资源引用计数：resourceId -> 引用次数
private final ConcurrentMap<String, AtomicInteger> resourceReferenceCounts = new ConcurrentHashMap<>();

// 资源引用者：resourceId -> 引用者集合（通常是插件ID）
private final ConcurrentMap<String, Set<String>> resourceReferences = new ConcurrentHashMap<>();
```

#### 新增方法
- `addResourceReference(resourceId, referenceId)` - 添加引用
- `removeResourceReference(resourceId, referenceId)` - 移除引用
- `getResourceReferenceCount(resourceId)` - 获取引用计数
- `getResourceReferences(resourceId)` - 获取引用者列表

### 3. 使用流程

#### 插件启动时
```java
// 在DataSourceConfig中
@Bean(name = "cuccDataSource")
public DataSource cuccDataSource() {
    // 使用带引用计数的方法，传入插件ID作为引用者
    return resourceRegistry.getDataSource(dbResourceId, "south-cucc-plugin");
}
```

#### 插件停止时
```java
// 在Plugin.onStop()中
@Override
public void onStop() {
    // 移除资源引用
    boolean canDestroy = resourceRegistry.removeResourceReference(dbResourceId, "south-cucc-plugin");
    if (canDestroy) {
        log.info("Resource {} has no more references, it can be safely destroyed", dbResourceId);
    } else {
        log.info("Resource {} still has other references: {}", 
                dbResourceId, resourceRegistry.getResourceReferences(dbResourceId));
    }
}
```

### 4. H2Resource改进

#### 支持DataSource重新创建
```java
// 保存HikariConfig配置以便重新创建
private HikariConfig hikariConfig;
private volatile boolean dataSourceClosed = false;

// 在doStart()中检查并重新创建DataSource
if (dataSourceClosed && hikariConfig != null) {
    recreateDataSource();
}
```

#### 智能停止策略
```java
@Override
protected void doStop() {
    // 只标记为已停止，不实际关闭DataSource
    dataSourceClosed = true;
}
```

## 使用示例

### 场景1：两个插件共享Resource

```java
// 插件A启动
DataSource dsA = resourceRegistry.getDataSource("shared-db", "plugin-a");
// 引用计数: 1, 引用者: [plugin-a]

// 插件B启动  
DataSource dsB = resourceRegistry.getDataSource("shared-db", "plugin-b");
// 引用计数: 2, 引用者: [plugin-a, plugin-b]
// dsA == dsB (同一个DataSource)

// 插件A停止
boolean canDestroy = resourceRegistry.removeResourceReference("shared-db", "plugin-a");
// canDestroy = false, 引用计数: 1, 引用者: [plugin-b]
// DataSource仍然可用

// 插件B停止
canDestroy = resourceRegistry.removeResourceReference("shared-db", "plugin-b");
// canDestroy = true, 引用计数: 0, 引用者: []
// 现在可以安全销毁Resource
```

### 场景2：DataSource自动恢复

```java
// 如果DataSource被意外关闭
DataSource ds = resourceRegistry.getDataSource("shared-db", "plugin-a");
// ResourceRegistry检测到DataSource已关闭，自动重启Resource
// 返回新的可用DataSource
```

## 关键特性

### 1. 线程安全
- 使用`ConcurrentHashMap`和`AtomicInteger`确保并发安全
- 支持多个插件同时启动/停止

### 2. 自动恢复
- 检测DataSource关闭状态
- 自动重新创建DataSource
- 保持配置信息以支持重建

### 3. 引用追踪
- 记录每个Resource的引用者
- 提供详细的引用信息用于调试

### 4. 向后兼容
- 保持原有API不变
- 新增带引用计数的重载方法

## 测试验证

### 单元测试
```bash
mvn test -Dtest=ResourceRegistryReferenceTest
```

### 集成测试
1. 启动两个使用相同Resource的插件
2. 停止其中一个插件
3. 验证另一个插件仍能正常使用Resource
4. 停止最后一个插件
5. 验证Resource被正确清理

## 注意事项

### 1. 插件开发者需要注意
- 在获取DataSource时传入插件ID
- 在插件停止时调用removeResourceReference
- 不要手动关闭共享的DataSource

### 2. 性能考虑
- 引用计数操作的开销很小
- 内存占用增加微乎其微
- 避免了频繁的Resource创建/销毁

### 3. 调试支持
- 提供引用计数查询方法
- 详细的日志记录
- 引用者列表追踪

## 扩展性

### 支持其他Resource类型
```java
// 可以扩展到其他Resource类型
public RedisTemplate<String, Object> getRedisTemplate(String resourceId, String referenceId);
public KafkaTemplate<String, String> getKafkaTemplate(String resourceId, String referenceId);
public MqttClient getMQTTClient(String resourceId, String referenceId);
```

### 支持引用权重
```java
// 未来可以支持不同权重的引用
public void addResourceReference(String resourceId, String referenceId, int weight);
```

### 支持引用回调
```java
// 未来可以支持引用变化回调
public void addResourceReferenceListener(String resourceId, ResourceReferenceListener listener);
```

## 总结

通过引用计数机制，我们解决了多插件共享Resource时的生命周期管理问题：

1. **避免误销毁**：只有当没有任何引用时才销毁Resource
2. **自动恢复**：检测并自动恢复已关闭的DataSource
3. **线程安全**：支持并发的插件启动/停止操作
4. **向后兼容**：不影响现有代码
5. **易于调试**：提供详细的引用信息

这个解决方案确保了插件系统的稳定性和可靠性，避免了因Resource共享导致的各种问题。

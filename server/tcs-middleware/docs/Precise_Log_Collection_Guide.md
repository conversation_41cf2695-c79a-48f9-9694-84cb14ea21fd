# 精确日志收集使用指南

## 问题背景

之前的日志收集实现存在问题：收集了整个middleware模块的所有日志，而不是特定资源或服务的日志。这导致日志噪音过多，难以定位特定组件的问题。

## 解决方案

### 1. 改进的日志过滤机制

修改了`MiddlewareLogCollector.isRelevantLog()`方法，使用更精确的过滤逻辑：

```java
private boolean isRelevantLog(ILoggingEvent eventObject) {
    String loggerName = eventObject.getLoggerName();
    String message = eventObject.getMessage();
    
    // 1. 检查Logger名称是否包含组件ID
    if (loggerName.contains(componentId)) {
        return true;
    }
    
    // 2. 检查消息内容是否明确包含组件ID
    if (message != null && message.contains(componentId)) {
        return true;
    }
    
    // 3. 检查是否是特定组件类型的Logger
    String expectedLoggerPrefix = "middleware." + componentType + "." + componentId;
    if (loggerName.startsWith(expectedLoggerPrefix)) {
        return true;
    }
    
    // 4. 检查调用栈中是否有与组件相关的类
    // ...
}
```

### 2. 专用Logger机制

为每个资源和服务创建专用的Logger：

- **Resource Logger**: `middleware.resource.{resourceId}`
- **Service Logger**: `middleware.service.{serviceId}`

### 3. ComponentLogger工具类

提供便捷的Logger获取和使用方法：

```java
// 获取Resource专用Logger
Logger logger = ComponentLogger.getResourceLogger("database-001");

// 获取Service专用Logger
Logger logger = ComponentLogger.getServiceLogger("http-service-001");

// 记录生命周期日志
ComponentLogger.logResourceLifecycle("database-001", "INFO", "Resource initialized");
ComponentLogger.logServiceLifecycle("http-service-001", "WARN", "Service warning: {}", details);
```

## 使用方法

### 1. 在Resource实现中使用

```java
@Component
public class DatabaseResourceImpl implements DatabaseResource {
    private static final Logger logger = ComponentLogger.getResourceLogger("database-001", DatabaseResourceImpl.class);
    
    @Override
    public void initialize() {
        logger.info("Initializing database resource");
        
        try {
            // 初始化逻辑
            logger.info("Database connection established");
        } catch (Exception e) {
            logger.error("Failed to initialize database resource", e);
            throw e;
        }
    }
    
    @Override
    public void connect() {
        logger.debug("Attempting to connect to database");
        // 连接逻辑
        logger.info("Successfully connected to database");
    }
}
```

### 2. 在Service实现中使用

```java
@Component
public class HttpServiceImpl implements HttpService {
    private static final Logger logger = ComponentLogger.getServiceLogger("http-service-001", HttpServiceImpl.class);
    
    @Override
    public void start() {
        logger.info("Starting HTTP service");
        
        try {
            // 启动逻辑
            logger.info("HTTP service started on port 8080");
        } catch (Exception e) {
            logger.error("Failed to start HTTP service", e);
            throw e;
        }
    }
    
    @Override
    public void processRequest(HttpRequest request) {
        logger.debug("Processing request: {}", request.getPath());
        
        try {
            // 处理逻辑
            logger.debug("Request processed successfully");
        } catch (Exception e) {
            logger.warn("Request processing failed: {}", e.getMessage());
        }
    }
}
```

### 3. 在运行时服务中使用

```java
@Service
public class ResourceRuntimeServiceImpl implements ResourceRuntimeService {
    
    @Override
    public ResourceInstantiationResult instantiateResource(String resourceConfigId) {
        // 使用专用Logger记录生命周期
        ComponentLogger.logResourceLifecycle(resourceConfigId, "INFO", "Starting resource instantiation");
        
        try {
            // 实例化逻辑
            ComponentLogger.logResourceLifecycle(resourceConfigId, "INFO", "Resource instantiated successfully");
            return ResourceInstantiationResult.success();
        } catch (Exception e) {
            ComponentLogger.logResourceLifecycle(resourceConfigId, "ERROR", "Resource instantiation failed: {}", e.getMessage());
            return ResourceInstantiationResult.failure(e.getMessage());
        }
    }
}
```

## 日志收集效果

### 修改前（收集所有middleware日志）

```
2025-01-28 10:00:01 [main] INFO  c.s.t.m.service.ResourceService - Some unrelated log
2025-01-28 10:00:02 [main] INFO  c.s.t.m.controller.ResourceController - Another unrelated log
2025-01-28 10:00:03 [main] INFO  middleware.resource.database-001 - Database resource initialized
2025-01-28 10:00:04 [main] INFO  c.s.t.m.service.ServiceService - More unrelated logs
```

### 修改后（只收集相关日志）

```
2025-01-28 10:00:03 [main] INFO  middleware.resource.database-001 - [database-001] Database resource initialized
2025-01-28 10:00:04 [main] DEBUG middleware.resource.database-001.DatabaseResourceImpl - Attempting to connect to database
2025-01-28 10:00:05 [main] INFO  middleware.resource.database-001 - [database-001] Successfully connected to database
```

## 最佳实践

### 1. Logger命名规范

- **Resource**: `middleware.resource.{resourceId}`
- **Service**: `middleware.service.{serviceId}`
- **实现类**: `middleware.resource.{resourceId}.{ClassName}`

### 2. 日志消息格式

- 包含组件ID：`[{componentId}] {message}`
- 使用参数化消息：`logger.info("Processing {} items", count)`
- 异常日志包含堆栈：`logger.error("Operation failed", exception)`

### 3. 日志级别使用

- **DEBUG**: 详细的调试信息
- **INFO**: 重要的业务流程信息
- **WARN**: 警告信息，不影响正常运行
- **ERROR**: 错误信息，影响功能正常运行

### 4. 生命周期日志

使用`ComponentLogger.logResourceLifecycle()`和`ComponentLogger.logServiceLifecycle()`记录关键生命周期事件：

- 初始化开始/完成
- 启动/停止
- 配置变更
- 错误和异常

## 验证方法

### 1. 单元测试

运行`ComponentLoggerTest`验证Logger功能：

```bash
mvn test -Dtest=ComponentLoggerTest
```

### 2. 集成测试

1. 创建资源配置
2. 实例化资源
3. 查看日志收集器是否只收集相关日志
4. 验证日志内容的准确性

### 3. 日志API测试

```bash
# 获取资源日志流
curl http://localhost:8080/middleware/resources/logs-stream?resourceId=database-001

# 获取最近100条日志
curl http://localhost:8080/middleware/resources/logs?resourceId=database-001&count=100
```

## 注意事项

1. **性能影响**: 精确的日志过滤可能会有轻微的性能开销，但相比于日志噪音的减少，这是值得的
2. **Logger缓存**: SLF4J会自动缓存Logger实例，相同名称的Logger会返回同一个实例
3. **日志级别**: 确保在生产环境中设置合适的日志级别，避免过多的DEBUG日志
4. **内存管理**: 日志收集器有最大日志数量限制（默认200条），超出会自动清理旧日志

## 故障排除

### 1. 日志收集不到

- 检查Logger名称是否正确
- 确认日志收集器是否已创建
- 验证日志级别设置

### 2. 收集到无关日志

- 检查`isRelevantLog()`方法的过滤逻辑
- 确认组件ID是否唯一
- 验证Logger名称规范

### 3. 日志重复

- 检查Logger的`additive`属性设置
- 确认是否重复添加Appender

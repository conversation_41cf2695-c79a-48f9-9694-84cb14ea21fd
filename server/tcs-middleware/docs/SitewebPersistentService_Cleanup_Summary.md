# SitewebPersistentService 清理总结

## 概述

本文档总结了对 SitewebPersistentService 重构后的清理工作，移除了所有为适配不需要 Resource 场景而建立的直接获取方法和特殊处理逻辑。

## 清理内容

### 1. 移除 ServiceRegistry 中的特殊方法

**文件**: `ServiceRegistry.java`

**移除内容**:
- `getSitewebPersistentService()` 方法
- 相关的导入语句：`ServiceType`、`SitewebPersistentService`

**原因**: 该方法用于创建不需要 Resource 的默认 SitewebPersistentService 实例，与重构后的设计不符。

### 2. 移除 IServiceInitializer 接口中的特殊方法

**文件**: `IServiceInitializer.java`

**移除内容**:
- `createDefaultSitewebPersistentService(String serviceId)` 方法定义

**原因**: 该方法专门用于创建不依赖数据库配置的默认实例，现在所有实例都必须关联 Resource。

### 3. 移除 ServiceLifecycleManagerImpl 中的实现

**文件**: `ServiceLifecycleManagerImpl.java`

**移除内容**:
- `createDefaultSitewebPersistentService(String serviceId)` 方法实现
- 该方法中的硬编码配置逻辑

**原因**: 移除了创建不需要 Resource 的服务实例的具体实现。

### 4. 简化 MwService 注解

**文件**: `MwService.java`

**修改内容**:
- 更新注释，移除关于 SITEWEB_PERSISTENT 类型特殊处理的说明
- 明确 type 属性仅用于类型验证

**原因**: 不再支持通过 type 属性自动创建默认服务。

### 5. 简化 MwServiceAnnotationProcessor

**文件**: `MwServiceAnnotationProcessor.java`

**移除内容**:
- 对 `ServiceType.SITEWEB_PERSISTENT` 的特殊处理逻辑
- 调用 `serviceRegistry.getSitewebPersistentService()` 的代码

**修改后逻辑**:
- 只支持通过明确的服务 ID 获取服务
- 如果没有提供服务 ID 且 required=true，则抛出异常

### 6. 更新使用文档

**文件**: `SitewebPersistentService使用指南.md`

**修改内容**:
- 移除"方式1：使用注解类型"的说明
- 更新示例代码，使用类型安全的方法调用
- 重新组织文档结构

### 7. 删除配置类

**文件**: `SitewebPersistentServiceConfig.java`

**操作**: 完全删除该文件

**原因**: 该配置类是为不需要 Resource 的场景设计的，现在不再需要。

## 影响分析

### 1. 破坏性变更

以下使用方式将不再支持：

```java
// ❌ 不再支持
@MwService(type = ServiceType.SITEWEB_PERSISTENT)
private SitewebPersistentService sitewebService;

// ❌ 不再支持
SitewebPersistentService service = serviceRegistry.getSitewebPersistentService();
```

### 2. 推荐的新使用方式

```java
// ✅ 推荐方式：使用明确的服务配置ID
@MwService("test-siteweb-persistent-service-001")
private SitewebPersistentService sitewebService;

// ✅ 或通过ServiceRegistry
SitewebPersistentService service = serviceRegistry.get("test-siteweb-persistent-service-001");
```

### 3. 配置要求

现在所有 SitewebPersistentService 实例都必须：

1. **关联数据库 Resource**: 必须在服务配置中指定 `resource_configuration_id`
2. **明确的服务 ID**: 不能依赖自动创建的默认实例
3. **完整的生命周期**: 通过中间件的服务管理系统创建和管理

## 迁移指南

### 1. 代码迁移

**步骤1**: 创建服务配置
```json
{
  "id": "my-siteweb-persistent-service",
  "type": "SITEWEB_PERSISTENT",
  "resourceId": "my-database-resource",
  "config": {}
}
```

**步骤2**: 更新注入方式
```java
// 修改前
@MwService(type = ServiceType.SITEWEB_PERSISTENT)
private SitewebPersistentService sitewebService;

// 修改后
@MwService("my-siteweb-persistent-service")
private SitewebPersistentService sitewebService;
```

### 2. 配置迁移

确保在数据库中有对应的服务配置记录：

```sql
INSERT INTO mw_service_configuration (
    id, service_id, name, description, 
    config, resource_configuration_id, status, created_by
) VALUES (
    'my-siteweb-persistent-service',
    'SITEWEB_PERSISTENT',
    'My Siteweb Persistent Service',
    'Custom Siteweb persistent service with dedicated database',
    '{}',
    'my-database-resource',
    'ENABLED',
    'system'
);
```

## 验证清理效果

### 1. 编译验证
确保所有引用已清理，项目能正常编译：
```bash
mvn clean compile
```

### 2. 测试验证
运行相关测试确保功能正常：
```bash
mvn test -Dtest=SitewebPersistentServiceRefactoredTest
```

### 3. 功能验证
- 验证通过明确服务ID能正常获取服务
- 验证服务能正确关联数据库Resource
- 验证服务的完整生命周期管理

## 总结

通过这次清理，我们：

1. ✅ **移除了所有绕过Resource的特殊处理逻辑**
2. ✅ **简化了服务获取机制，统一使用服务ID**
3. ✅ **确保了架构的一致性和完整性**
4. ✅ **提供了清晰的迁移路径**

现在 SitewebPersistentService 完全遵循中间件的标准服务模式：
- 必须关联Resource
- 通过配置管理
- 完整的生命周期
- 统一的获取方式

这为后续的功能扩展和维护奠定了坚实的基础。

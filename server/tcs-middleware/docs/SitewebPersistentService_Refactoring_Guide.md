# SitewebPersistentService 重构指南

## 概述

本文档描述了对 `SitewebPersistentService` 的重构，使其从直接使用 tcs-core 数据源改为关联关系型数据库 Resource，并使用 Resource 的 DataSource 创建独立的 MyBatis 配置。

## 重构目标

1. **数据源隔离**：每个 SitewebPersistentService 实例使用独立的数据库 Resource
2. **配置灵活性**：支持不同的数据库类型（H2、MySQL、PostgreSQL）
3. **资源管理**：利用中间件的 Resource 生命周期管理
4. **向后兼容**：保持现有 API 不变

## 重构内容

### 1. 修改服务类型定义

**文件**: `ServiceType.java`

```java
// 修改前
SITEWEB_PERSISTENT("SITEWEB_PERSISTENT", "NONE"),

// 修改后
SITEWEB_PERSISTENT("SITEWEB_PERSISTENT", "RELATIONAL_DB"),
```

### 2. 创建 MyBatis 配置类

**新增文件**: `SitewebMyBatisConfig.java`

提供静态方法用于创建独立的 SqlSessionFactory 和 SqlSessionTemplate：

- `createSqlSessionFactory(DataSource, String)` - 创建 SqlSessionFactory
- `createSqlSessionTemplate(SqlSessionFactory, String)` - 创建 SqlSessionTemplate
- `validateDataSource(DataSource, String)` - 验证 DataSource

### 3. 重构 SitewebPersistentService

**主要变更**:

1. **构造函数**: 现在接受 Resource 参数
2. **新增字段**: 
   - `SqlSessionFactory sqlSessionFactory`
   - `SqlSessionTemplate sqlSessionTemplate`
3. **生命周期方法**:
   - `doInitialize()`: 验证 Resource 类型
   - `doStart()`: 创建 MyBatis 配置
   - `doDestroy()`: 清理资源
4. **健康检查**: 增加 Resource 和 MyBatis 组件的健康检查

### 4. 更新 SitewebPersistentServiceProvider

**主要变更**:

1. **Resource 验证**: 确保提供了有效的数据库 Resource
2. **类型检查**: 验证 Resource 类型是否支持
3. **服务创建**: 传入 Resource 参数

### 5. 更新数据库初始化脚本

**修改内容**:

1. 服务类型的 `supported_resource_category` 从 `NONE` 改为 `RELATIONAL_DB`
2. 测试服务配置关联具体的数据库 Resource

## 支持的资源类型

- **H2**: 内存数据库和文件数据库
- **MySQL**: MySQL 关系型数据库
- **PostgreSQL**: PostgreSQL 关系型数据库

## 配置示例

### 1. 资源配置

```json
{
  "id": "siteweb-h2-db",
  "type": "H2",
  "config": {
    "dbName": "siteweb_data",
    "mode": "FILE",
    "filePath": "./data/siteweb.db",
    "username": "sa",
    "password": "",
    "maxPoolSize": 10,
    "minIdle": 2
  }
}
```

### 2. 服务配置

```json
{
  "id": "siteweb-persistent-001",
  "type": "SITEWEB_PERSISTENT",
  "resourceId": "siteweb-h2-db",
  "config": {}
}
```

## 使用方式

### 1. 通过注解使用

```java
@Component
public class MyBusinessService {
    
    @MwService("siteweb-persistent-001")
    private SitewebPersistentService sitewebService;
    
    public void processData() {
        // 使用独立数据库的 siteweb 服务
        List<House> houses = sitewebService.list(House.class);
    }
}
```

### 2. 通过服务注册表使用

```java
SitewebPersistentService service = serviceRegistry.get("siteweb-persistent-001");
House house = service.getById(House.class, 1001);
```

## 优势

### 1. 数据源隔离
- 每个服务实例使用独立的数据库
- 避免数据混淆和冲突
- 支持多租户场景

### 2. 配置灵活
- 可以为不同业务场景配置不同的数据库
- 支持开发、测试、生产环境使用不同的数据库类型
- 支持数据库迁移和升级

### 3. 资源管理
- 利用中间件的 Resource 生命周期管理
- 自动处理连接池的创建和销毁
- 支持资源引用计数和共享

### 4. 性能优化
- 独立的 MyBatis 配置，避免配置冲突
- 专用的连接池，提高性能
- 支持数据库特定的优化配置

## 迁移指南

### 1. 现有代码兼容性
- API 保持不变，现有代码无需修改
- 服务行为保持一致

### 2. 配置迁移
1. 创建数据库 Resource 配置
2. 修改服务配置，关联 Resource
3. 更新部署配置

### 3. 测试验证
1. 运行单元测试验证功能
2. 进行集成测试确保兼容性
3. 性能测试验证优化效果

## 注意事项

1. **Resource 依赖**: 服务现在必须关联一个数据库 Resource
2. **类型限制**: 只支持关系型数据库 Resource
3. **配置验证**: 启动时会验证 Resource 类型和连接
4. **生命周期**: 服务的生命周期与 Resource 相关联

## 测试

运行重构测试：

```bash
mvn test -Dtest=SitewebPersistentServiceRefactoredTest
```

## 总结

通过这次重构，SitewebPersistentService 实现了：

1. ✅ 数据源隔离和独立配置
2. ✅ 支持多种数据库类型
3. ✅ 完整的资源生命周期管理
4. ✅ 向后兼容的 API
5. ✅ 灵活的配置管理

这为后续的多租户支持、数据库迁移和性能优化奠定了基础。

# SitewebPersistentService 使用指南

## 概述

SitewebPersistentService 是中间件提供的一个特殊服务，用于封装 tcs-siteweb 模块中的各种业务服务。它提供了统一的接口来访问 siteweb 模块的持久化功能，避免了直接依赖 tcs-siteweb 模块。

## 主要特性

1. **无Resource依赖**：不需要配置任何Resource，直接通过Spring容器访问服务
2. **具体业务方法**：提供具体的业务方法，如 `getHouse()`、`getSamplerByProtocolCode()` 等
3. **反射封装**：内部使用反射调用 tcs-siteweb 的服务，避免直接依赖
4. **类型安全**：虽然返回Object类型，但提供了清晰的方法签名和文档
5. **异常处理**：统一的异常处理机制

## 使用方式

### 使用配置ID

```java
@Component
public class MyBusinessService {

    // 使用配置ID，获取特定配置的SitewebPersistentService
    @MwService("test-siteweb-persistent-service-001")
    private SitewebPersistentService sitewebService;

    public void processHouseData() {
        try {
            // 查询所有局房
            List<House> houses = sitewebService.list(House.class);

            // 根据ID查询特定局房
            House house = sitewebService.getById(House.class, 1);

            // 查询站点默认局房
            House defaultHouse = sitewebService.findStationDefaultHouse(100);

        } catch (MiddlewareTechnicalException e) {
            // 处理异常
            logger.error("处理局房数据失败", e);
        }
    }
}
```

### 通过ServiceRegistry获取

```java
@Component
public class MyBusinessService {

    @Autowired
    private ServiceRegistry serviceRegistry;

    public void doSomething() {
        // 获取默认的SitewebPersistentService
        SitewebPersistentService service = serviceRegistry.getSitewebPersistentService();

        // 使用服务
        List<?> samplers = service.getAllSamplers();
    }
}
```

## 可用的业务方法

### 通用IService方法（推荐）

这些方法支持所有继承自`IService<Entity>`的服务，通过实体类型自动匹配对应的服务：

```java
// 根据ID查询实体（类型安全）
House house = sitewebService.getById(House.class, 1);
Sampler sampler = sitewebService.getById(Sampler.class, 1);
Equipment equipment = sitewebService.getById(Equipment.class, 1);

// 查询所有实体
List<House> allHouses = sitewebService.list(House.class);
List<Sampler> allSamplers = sitewebService.list(Sampler.class);

// 保存实体
House newHouse = new House();
// 设置属性...
boolean saved = sitewebService.save(newHouse);

// 删除实体
boolean deleted = sitewebService.removeById(House.class, 1);
```

### 特殊方法封装

对于有特殊业务方法的服务，提供专门的封装方法：

```java
// [HouseService] 根据ID查询局房
Object house = sitewebService.findHouseById(Integer houseId);

// [HouseService] 查询站点的默认局房
Object defaultHouse = sitewebService.findStationDefaultHouse(Integer stationId);

// [SamplerService] 根据协议码查询采集器
Object sampler = sitewebService.findSamplerByProtocolCode(String protocolCode);
```

## 返回值处理

由于使用反射调用，返回值类型为 `Object` 或 `List<?>`。您可以通过以下方式处理：

### 方式1：直接使用（推荐）

```java
Object house = sitewebService.getHouse(1);
if (house != null) {
    // 直接使用toString()或日志输出
    logger.info("找到局房: {}", house);
}
```

### 方式2：反射获取属性

```java
Object house = sitewebService.getHouse(1);
if (house != null) {
    try {
        Method getHouseNameMethod = house.getClass().getMethod("getHouseName");
        String houseName = (String) getHouseNameMethod.invoke(house);
        logger.info("局房名称: {}", houseName);
    } catch (Exception e) {
        logger.error("获取局房名称失败", e);
    }
}
```

### 方式3：类型转换（需要依赖tcs-siteweb）

```java
// 如果您的模块已经依赖了tcs-siteweb，可以直接转换
Object houseObj = sitewebService.getHouse(1);
if (houseObj instanceof House) {
    House house = (House) houseObj;
    String houseName = house.getHouseName();
}
```

## 异常处理

所有方法都可能抛出 `MiddlewareTechnicalException`，建议统一处理：

```java
try {
    Object house = sitewebService.getHouse(1);
    // 处理业务逻辑
} catch (MiddlewareTechnicalException e) {
    logger.error("查询局房失败: {}", e.getMessage(), e);
    // 根据业务需要进行错误处理
}
```

## 健康检查

```java
// 检查服务是否健康
boolean isHealthy = sitewebService.isHealthy();

// 获取详细健康状态
HealthStatus healthStatus = sitewebService.checkHealth();
logger.info("服务健康状态: {}", healthStatus.getMessage());
```

## 扩展新的业务方法

如果需要添加新的业务方法，请在 `SitewebPersistentService` 类中添加：

```java
/**
 * 新的业务方法示例
 */
public Object getEquipmentById(Integer equipmentId) throws MiddlewareTechnicalException {
    try {
        // 获取对应的service
        Object equipmentService = getSitewebService("equipmentServiceImpl", Object.class);

        // 使用反射调用方法
        Method method = equipmentService.getClass().getMethod("getById", Integer.class);
        Object result = method.invoke(equipmentService, equipmentId);

        return result;

    } catch (Exception e) {
        logger.error("Failed to get equipment by ID: {}", equipmentId, e);
        throw new MiddlewareTechnicalException(
            MiddlewareTechnicalErrorCode.SERVICE_INITIALIZATION_FAILED,
            "Failed to get equipment by ID: " + equipmentId,
            e
        );
    }
}
```

## 注意事项

1. **性能考虑**：由于使用反射，性能会比直接调用稍低，但在可接受范围内
2. **类型安全**：返回Object类型，需要注意类型处理
3. **异常处理**：所有方法都可能抛出异常，需要适当处理
4. **服务名称**：内部使用的服务名称（如"houseServiceImpl"）需要与Spring容器中的Bean名称一致
5. **方法签名**：反射调用的方法签名必须与目标服务的方法完全匹配

## 测试

参考 `SitewebPersistentServiceTest` 类了解如何测试各种功能。

## 配置

在数据库中已经预配置了相关的服务类型和测试配置，无需额外配置即可使用。

package com.siteweb.tcs.middleware.controller.crud;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.siteweb.tcs.common.response.ResponseResult;
import com.siteweb.tcs.middleware.entity.ServiceTypeEntity;
import com.siteweb.tcs.middleware.service.ServiceTypeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 服务类型控制器
 * 提供服务类型的查询接口
 */
@RestController
@RequestMapping("/middleware/service-types")
@Tag(name = "服务类型管理", description = "服务类型的查询接口")
public class ServiceTypeController {

    @Autowired
    private ServiceTypeService serviceTypeService;

    /**
     * 分页查询服务类型
     *
     * @param current 当前页
     * @param size 每页大小
     * @param name 服务类型名称（可选，用于模糊查询）
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "分页查询服务类型", description = "分页查询服务类型，支持按名称模糊查询")
    public ResponseResult<IPage<ServiceTypeEntity>> page(
            @Parameter(description = "当前页码", required = true) @RequestParam(defaultValue = "1") long current,
            @Parameter(description = "每页大小", required = true) @RequestParam(defaultValue = "10") long size,
            @Parameter(description = "服务类型名称（可选）") @RequestParam(required = false) String name) {

        Page<ServiceTypeEntity> page = new Page<>(current, size);
        IPage<ServiceTypeEntity> result = serviceTypeService.pageServiceTypes(page, name);

        return ResponseResult.success(result);
    }

    /**
     * 获取所有服务类型
     *
     * @return 所有服务类型列表
     */
    @GetMapping("/list")
    @Operation(summary = "获取所有服务类型", description = "获取所有服务类型列表")
    public ResponseResult<List<ServiceTypeEntity>> list() {
        List<ServiceTypeEntity> list = serviceTypeService.list();
        return ResponseResult.success(list);
    }

    /**
     * 根据ID获取服务类型
     *
     * @param id 服务类型ID
     * @return 服务类型详情
     */
    @GetMapping("/{id}")
    @Operation(summary = "根据ID获取服务类型", description = "根据ID获取服务类型详情")
    public ResponseResult<ServiceTypeEntity> getById(@PathVariable String id) {
        ServiceTypeEntity serviceType = serviceTypeService.getServiceTypeById(id);
        return ResponseResult.success(serviceType);
    }

    /**
     * 根据支持的资源类别获取服务类型列表
     *
     * @param supportedResourceCategory 支持的资源类别
     * @return 服务类型列表
     */
    @GetMapping("/supported-resource-category/{supportedResourceCategory}")
    @Operation(summary = "根据支持的资源类别获取服务类型", description = "根据支持的资源类别获取服务类型列表")
    public ResponseResult<List<ServiceTypeEntity>> getBySupportedResourceCategory(@PathVariable String supportedResourceCategory) {
        List<ServiceTypeEntity> list = serviceTypeService.listServiceTypesBySupportedResourceCategory(supportedResourceCategory);
        return ResponseResult.success(list);
    }
}

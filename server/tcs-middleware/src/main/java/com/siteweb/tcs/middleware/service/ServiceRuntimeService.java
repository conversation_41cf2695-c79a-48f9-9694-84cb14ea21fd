package com.siteweb.tcs.middleware.service;

import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.dto.ServiceInstantiationResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;

import java.util.List;
import java.util.Map;

/**
 * 服务运行时服务接口
 * 提供服务的运行时操作，如连接测试、实例化、健康检查等
 */
public interface ServiceRuntimeService {

    /**
     * 测试服务连接
     *
     * @param serviceTypeId 服务类型ID
     * @param config 服务配置
     * @return 连接测试结果
     */
    ConnectionTestResult testConnection(String serviceTypeId, Map<String, Object> config);

    /**
     * 验证服务配置
     *
     * @param serviceTypeId 服务类型ID
     * @param config 服务配置
     * @return 验证结果
     */
    ValidationResult validateConfig(String serviceTypeId, Map<String, Object> config);

    /**
     * 检查服务是否已实例化
     *
     * @param serviceConfigId 服务配置ID
     * @return 是否已实例化
     */
    boolean isServiceInstantiated(String serviceConfigId);

    /**
     * 批量检查服务是否已实例化
     *
     * @param serviceConfigIds 服务配置ID列表
     * @return 服务配置ID到实例化状态的映射
     */
    Map<String, Boolean> batchCheckServiceInstantiated(List<String> serviceConfigIds);

    /**
     * 实例化服务
     *
     * @param serviceConfigId 服务配置ID
     * @return 服务实例化结果
     */
    ServiceInstantiationResult instantiateService(String serviceConfigId);

    /**
     * 销毁服务实例
     *
     * @param serviceConfigId 服务配置ID
     * @return 是否成功销毁
     */
    boolean destroyService(String serviceConfigId);

    /**
     * 检查服务健康状态
     *
     * @param serviceConfigId 服务配置ID
     * @return 健康状态
     */
    HealthStatus checkServiceHealth(String serviceConfigId);
}

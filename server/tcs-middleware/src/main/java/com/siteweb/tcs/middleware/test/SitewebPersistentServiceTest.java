package com.siteweb.tcs.middleware.test;

import com.siteweb.tcs.middleware.common.annotation.MwService;
import com.siteweb.tcs.middleware.common.service.ServiceType;
import com.siteweb.tcs.middleware.common.service.SitewebPersistentService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * SitewebPersistentService测试类
 * 演示新的API使用方式：通用IService方法 + 特殊方法封装
 */
@Component
public class SitewebPersistentServiceTest {

    private static final Logger logger = LoggerFactory.getLogger(SitewebPersistentServiceTest.class);

    // 使用注解注入SitewebPersistentService，指定type为SITEWEB_PERSISTENT
    @MwService("test-siteweb-persistent-service-001")
    private SitewebPersistentService sitewebService;


    /**
     * 测试服务健康状态
     */
    public void testServiceHealth() {
        try {
            logger.info("=== 测试SitewebPersistentService健康状态 ===");

            if (sitewebService != null) {
                boolean isHealthy = sitewebService.isHealthy();
                logger.info("SitewebPersistentService健康状态: {}", isHealthy ? "健康" : "不健康");

                var healthStatus = sitewebService.checkHealth();
                logger.info("详细健康状态: {}", healthStatus.getMessage());
            } else {
                logger.error("SitewebPersistentService未注入");
            }

        } catch (Exception e) {
            logger.error("测试服务健康状态时发生异常", e);
        }
    }

    /**
     * 测试服务发现机制
     */
    public void testServiceDiscovery() {
        try {
            logger.info("=== 测试服务发现机制 ===");

            if (sitewebService == null) {
                logger.error("SitewebPersistentService未注入，无法测试服务发现");
                return;
            }

            // 测试一些常见的实体类型
            String[] entityClassNames = {
                "com.siteweb.tcs.siteweb.entity.House",
                "com.siteweb.tcs.siteweb.entity.Equipment",
            };

            for (String entityClassName : entityClassNames) {
                try {
                    Class<?> entityClass = Class.forName(entityClassName);
                    logger.info("尝试发现{}的服务...", entityClass.getSimpleName());

                    // 通过调用list方法来触发服务发现
                    List<?> entities = sitewebService.list(entityClass);
                    logger.info("成功发现{}的服务，查询到{}条记录",
                        entityClass.getSimpleName(), entities.size());

                } catch (ClassNotFoundException e) {
                    logger.warn("实体类{}不存在", entityClassName);
                } catch (Exception e) {
                    logger.error("发现{}服务失败: {}", entityClassName, e.getMessage());
                }
            }

        } catch (Exception e) {
            logger.error("测试服务发现机制时发生异常", e);
        }
    }

    /**
     * 测试通用IService方法 - 使用反射获取实体类
     * 由于无法直接import tcs-siteweb的实体类，这里使用反射方式演示
     */
    public void testGenericServiceMethods() {
        try {
            logger.info("=== 测试通用IService方法（反射方式） ===");

            // 测试House实体的通用方法
            testEntityOperations("com.siteweb.tcs.siteweb.entity.House", "House");

            // 测试Sampler实体的通用方法
            testEntityOperations("com.siteweb.tcs.siteweb.entity.Sampler", "Sampler");

            // 测试Equipment实体的通用方法
            testEntityOperations("com.siteweb.tcs.siteweb.entity.Equipment", "Equipment");

        } catch (Exception e) {
            logger.error("测试通用IService方法时发生异常", e);
        }
    }

    /**
     * 测试特定实体的通用操作
     */
    private void testEntityOperations(String entityClassName, String entityName) {
        try {
            logger.info("--- 测试{}实体的通用操作 ---", entityName);

            // 通过反射获取实体类
            Class<?> entityClass = Class.forName(entityClassName);

            // 测试list方法 - 查询所有实体
            List<?> entities = sitewebService.list(entityClass);
            logger.info("查询到的{}总数: {}", entityName, entities.size());

            // 如果有数据，测试getById方法
            if (!entities.isEmpty()) {
                Object firstEntity = entities.get(0);
                logger.info("第一个{}: {}", entityName, firstEntity);

                // 尝试获取ID字段进行getById测试
                try {
                    // 常见的ID字段名称
                    String[] idFieldNames = {
                        entityName.toLowerCase() + "Id",  // houseId, samplerId等
                        "id",                             // 通用id字段
                        entityName.toLowerCase() + "_id"  // house_id等
                    };

                    Object entityId = null;
                    for (String fieldName : idFieldNames) {
                        try {
                            java.lang.reflect.Method getIdMethod = firstEntity.getClass().getMethod(
                                "get" + fieldName.substring(0, 1).toUpperCase() + fieldName.substring(1)
                            );
                            entityId = getIdMethod.invoke(firstEntity);
                            if (entityId != null) {
                                logger.info("找到{}的ID字段: {} = {}", entityName, fieldName, entityId);
                                break;
                            }
                        } catch (Exception ignored) {
                            // 尝试下一个字段名
                        }
                    }

                    // 如果找到ID，测试getById方法
                    if (entityId != null && entityId instanceof java.io.Serializable) {
                        Object foundEntity = sitewebService.getById(entityClass, (java.io.Serializable) entityId);
                        if (foundEntity != null) {
                            logger.info("通过ID {}查询{}成功", entityId, entityName);
                        } else {
                            logger.warn("通过ID {}查询{}失败", entityId, entityName);
                        }
                    } else {
                        logger.warn("无法获取{}的有效ID，跳过getById测试", entityName);
                    }

                } catch (Exception e) {
                    logger.warn("测试{}的getById方法时发生异常: {}", entityName, e.getMessage());
                }
            } else {
                logger.info("数据库中没有{}数据", entityName);
            }

        } catch (ClassNotFoundException e) {
            logger.warn("实体类{}不存在，跳过测试", entityClassName);
        } catch (Exception e) {
            logger.error("测试{}实体操作时发生异常", entityName, e);
        }
    }

    /**
     * 测试特殊方法封装
     */
    public void testSpecialMethods() {
        try {
            logger.info("=== 测试特殊方法封装 ===");

            // 测试House相关特殊方法
            testHouseSpecialMethods();

            // 测试Sampler相关特殊方法
            testSamplerSpecialMethods();

        } catch (Exception e) {
            logger.error("测试特殊方法时发生异常", e);
        }
    }

    /**
     * 测试House相关特殊方法
     */
    private void testHouseSpecialMethods() {
        try {
            logger.info("--- 测试House特殊方法 ---");

            // 测试findHouseById方法
            Object house = sitewebService.findHouseById(1);
            if (house != null) {
                logger.info("通过findHouseById(1)查询到House: {}", house);

                // 尝试获取House的stationId进行下一步测试
                try {
                    java.lang.reflect.Method getStationIdMethod = house.getClass().getMethod("getStationId");
                    Object stationId = getStationIdMethod.invoke(house);
                    if (stationId instanceof Integer) {
                        // 测试findStationDefaultHouse方法
                        Object defaultHouse = sitewebService.findStationDefaultHouse((Integer) stationId);
                        if (defaultHouse != null) {
                            logger.info("查询到站点{}的默认局房: {}", stationId, defaultHouse);
                        } else {
                            logger.info("站点{}没有默认局房", stationId);
                        }
                    }
                } catch (Exception e) {
                    logger.warn("无法获取House的stationId: {}", e.getMessage());
                }
            } else {
                logger.info("未找到ID为1的House");

                // 尝试测试一个可能存在的stationId
                Object defaultHouse = sitewebService.findStationDefaultHouse(100);
                if (defaultHouse != null) {
                    logger.info("查询到站点100的默认局房: {}", defaultHouse);
                } else {
                    logger.info("站点100没有默认局房");
                }
            }

        } catch (Exception e) {
            logger.error("测试House特殊方法时发生异常", e);
        }
    }

    /**
     * 测试Sampler相关特殊方法
     */
    private void testSamplerSpecialMethods() {
        try {
            logger.info("--- 测试Sampler特殊方法 ---");

            // 测试常见的协议码
            String[] protocolCodes = {"TCP", "UDP", "HTTP", "MQTT", "MODBUS"};

            for (String protocolCode : protocolCodes) {
                Object sampler = sitewebService.findSamplerByProtocolCode(protocolCode);
                if (sampler != null) {
                    logger.info("通过协议码{}查询到Sampler: {}", protocolCode, sampler);
                    break; // 找到一个就够了
                } else {
                    logger.debug("未找到协议码为{}的Sampler", protocolCode);
                }
            }

        } catch (Exception e) {
            logger.error("测试Sampler特殊方法时发生异常", e);
        }
    }

    /**
     * 测试性能和缓存机制
     */
    public void testPerformanceAndCaching() {
        try {
            logger.info("=== 测试性能和缓存机制 ===");

            // 测试多次调用同一实体类型的性能
            Class<?> houseClass = Class.forName("com.siteweb.tcs.siteweb.entity.House");

            long startTime = System.currentTimeMillis();

            // 第一次调用（会进行服务发现和缓存）
            List<?> houses1 = sitewebService.list(houseClass);
            long firstCallTime = System.currentTimeMillis() - startTime;

            // 第二次调用（应该使用缓存）
            startTime = System.currentTimeMillis();
            List<?> houses2 = sitewebService.list(houseClass);
            long secondCallTime = System.currentTimeMillis() - startTime;

            logger.info("第一次调用耗时: {}ms, 第二次调用耗时: {}ms", firstCallTime, secondCallTime);
            logger.info("缓存效果: {}倍提升", (double) firstCallTime / secondCallTime);

            // 验证结果一致性
            if (houses1.size() == houses2.size()) {
                logger.info("缓存结果一致性验证通过");
            } else {
                logger.warn("缓存结果不一致！");
            }

        } catch (Exception e) {
            logger.error("测试性能和缓存时发生异常", e);
        }
    }

    /**
     * 测试错误处理和边界情况
     */
    public void testErrorHandlingAndEdgeCases() {
        try {
            logger.info("=== 测试错误处理和边界情况 ===");

            // 测试null参数
            try {
                Object result = sitewebService.getById(null, 1);
                logger.info("null实体类测试: {}", result);
            } catch (Exception e) {
                logger.info("null实体类正确抛出异常: {}", e.getMessage());
            }

            // 测试不存在的实体类
            try {
                Class<?> fakeClass = String.class; // 使用一个不是实体的类
                List<?> result = sitewebService.list(fakeClass);
                logger.info("非实体类测试结果: {}", result.size());
            } catch (Exception e) {
                logger.info("非实体类正确抛出异常: {}", e.getMessage());
            }

            // 测试不存在的ID
            try {
                Class<?> houseClass = Class.forName("com.siteweb.tcs.siteweb.entity.House");
                Object result = sitewebService.getById(houseClass, -999999);
                logger.info("不存在ID测试结果: {}", result);
            } catch (Exception e) {
                logger.info("不存在ID测试异常: {}", e.getMessage());
            }

            // 测试特殊方法的边界情况
            Object result1 = sitewebService.findHouseById(null);
            logger.info("findHouseById(null)结果: {}", result1);

            Object result2 = sitewebService.findSamplerByProtocolCode("");
            logger.info("findSamplerByProtocolCode(\"\")结果: {}", result2);

            Object result3 = sitewebService.findSamplerByProtocolCode(null);
            logger.info("findSamplerByProtocolCode(null)结果: {}", result3);

        } catch (Exception e) {
            logger.error("测试错误处理时发生异常", e);
        }
    }

    /**
     * 演示实际业务场景
     */
    public void demonstrateBusinessScenarios() {
        try {
            logger.info("=== 演示实际业务场景 ===");

            // 场景1: 查询站点的所有设备信息
            demonstrateStationEquipmentQuery();

            // 场景2: 查询采集器及其相关信息
            demonstrateSamplerInfoQuery();

        } catch (Exception e) {
            logger.error("演示业务场景时发生异常", e);
        }
    }

    private void demonstrateStationEquipmentQuery() {
        try {
            logger.info("--- 业务场景1: 查询站点设备信息 ---");

            // 1. 查询所有局房
            Class<?> houseClass = Class.forName("com.siteweb.tcs.siteweb.entity.House");
            List<?> houses = sitewebService.list(houseClass);
            logger.info("系统中共有{}个局房", houses.size());

            if (!houses.isEmpty()) {
                Object firstHouse = houses.get(0);
                logger.info("示例局房: {}", firstHouse);

                // 2. 查询设备
                Class<?> equipmentClass = Class.forName("com.siteweb.tcs.siteweb.entity.Equipment");
                List<?> equipments = sitewebService.list(equipmentClass);
                logger.info("系统中共有{}个设备", equipments.size());
            }

        } catch (Exception e) {
            logger.error("演示站点设备查询场景时发生异常", e);
        }
    }

    private void demonstrateSamplerInfoQuery() {
        try {
            logger.info("--- 业务场景2: 查询采集器信息 ---");

            // 1. 查询所有采集器
            Class<?> samplerClass = Class.forName("com.siteweb.tcs.siteweb.entity.Sampler");
            List<?> samplers = sitewebService.list(samplerClass);
            logger.info("系统中共有{}个采集器", samplers.size());

            // 2. 尝试通过特殊方法查询特定协议的采集器
            Object tcpSampler = sitewebService.findSamplerByProtocolCode("TCP");
            if (tcpSampler != null) {
                logger.info("找到TCP协议采集器: {}", tcpSampler);
            }

        } catch (Exception e) {
            logger.error("演示采集器信息查询场景时发生异常", e);
        }
    }

    /**
     * 执行所有测试
     */
    public void runAllTests() {
        logger.info("========================================");
        logger.info("开始执行SitewebPersistentService完整测试");
        logger.info("========================================");

        testServiceHealth();
        testServiceDiscovery();
        testGenericServiceMethods();
        testSpecialMethods();
        testPerformanceAndCaching();
        testErrorHandlingAndEdgeCases();
        demonstrateBusinessScenarios();

        logger.info("========================================");
        logger.info("SitewebPersistentService测试执行完毕");
        logger.info("========================================");
    }

    /**
     * 快速测试 - 只执行核心功能测试
     */
    public void runQuickTests() {
        logger.info("=== 执行SitewebPersistentService快速测试 ===");

        testServiceHealth();
        testServiceDiscovery();
        testGenericServiceMethods();
        testSpecialMethods();

        logger.info("=== 快速测试执行完毕 ===");
    }
}

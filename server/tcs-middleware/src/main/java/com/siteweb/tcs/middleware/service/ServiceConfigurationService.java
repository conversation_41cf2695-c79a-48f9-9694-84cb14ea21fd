package com.siteweb.tcs.middleware.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.middleware.entity.ServiceConfigurationEntity;

import java.util.List;

/**
 * 服务配置服务接口
 */
public interface ServiceConfigurationService extends IService<ServiceConfigurationEntity> {
    
    /**
     * 分页查询服务配置
     *
     * @param page 分页参数
     * @param serviceId 服务类型ID，可为null
     * @param name 服务配置名称，可为null
     * @param status 服务配置状态，可为null
     * @return 分页结果
     */
    IPage<ServiceConfigurationEntity> pageServiceConfigurations(
            Page<ServiceConfigurationEntity> page,
            String serviceId,
            String name,
            String status);
    
    /**
     * 根据ID查询服务配置
     *
     * @param id 服务配置ID
     * @return 服务配置实体
     */
    ServiceConfigurationEntity getServiceConfigurationById(String id);
    
    /**
     * 创建服务配置
     *
     * @param serviceConfiguration 服务配置实体
     * @return 创建后的服务配置实体
     */
    ServiceConfigurationEntity createServiceConfiguration(ServiceConfigurationEntity serviceConfiguration);
    
    /**
     * 更新服务配置
     *
     * @param id 服务配置ID
     * @param serviceConfiguration 服务配置实体
     * @return 更新后的服务配置实体
     */
    ServiceConfigurationEntity updateServiceConfiguration(String id, ServiceConfigurationEntity serviceConfiguration);
    
    /**
     * 删除服务配置
     *
     * @param id 服务配置ID
     * @return 是否删除成功
     */
    boolean deleteServiceConfiguration(String id);
    
    /**
     * 根据服务类型ID查询服务配置列表
     *
     * @param serviceId 服务类型ID
     * @return 服务配置列表
     */
    List<ServiceConfigurationEntity> listServiceConfigurationsByServiceId(String serviceId);
    
    /**
     * 根据资源配置ID查询服务配置列表
     *
     * @param resourceConfigurationId 资源配置ID
     * @return 服务配置列表
     */
    List<ServiceConfigurationEntity> listServiceConfigurationsByResourceConfigurationId(String resourceConfigurationId);
}

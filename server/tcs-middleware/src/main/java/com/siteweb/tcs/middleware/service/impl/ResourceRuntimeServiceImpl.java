package com.siteweb.tcs.middleware.service.impl;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.lifecycle.ResourceLifecycleManager;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.dto.ResourceInstantiationResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.resource.Resource;
import com.siteweb.tcs.middleware.common.resource.ResourceProvider;
import com.siteweb.tcs.middleware.common.resource.ResourceProviderFactory;
import com.siteweb.tcs.middleware.entity.ResourceTypeEntity;
import com.siteweb.tcs.middleware.service.ResourceRuntimeService;
import com.siteweb.tcs.middleware.service.ResourceTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 资源运行时服务实现类
 */
@Slf4j
@Service
public class ResourceRuntimeServiceImpl implements ResourceRuntimeService {

    @Autowired
    private ResourceProviderFactory resourceProviderFactory;

    @Autowired
    private ResourceRegistry resourceRegistry;

    @Autowired
    private ResourceLifecycleManager resourceLifecycleManager;

    @Autowired
    private ResourceTypeService resourceTypeService;

    @Override
    public ConnectionTestResult testConnection(String resourceTypeId, Map<String, Object> config) {
        try {
            // 获取资源类型
            ResourceTypeEntity resourceTypeEntity = resourceTypeService.getResourceTypeById(resourceTypeId);
            if (resourceTypeEntity == null) {
                return new ConnectionTestResult(false, "资源类型不存在: " + resourceTypeId);
            }

            // 获取资源提供者
            ResourceProvider<? extends Resource> provider = resourceProviderFactory.getProvider(resourceTypeId);
            if (provider == null) {
                return new ConnectionTestResult(false, "找不到资源提供者: " + resourceTypeId);
            }

            // 测试连接
            return provider.testConnection(config);
        } catch (Exception e) {
            log.error("测试资源连接失败", e);
            return new ConnectionTestResult(false, "测试连接失败: " + e.getMessage());
        }
    }

    @Override
    public ValidationResult validateConfig(String resourceTypeId, Map<String, Object> config) {
        try {
            // 获取资源类型
            ResourceTypeEntity resourceTypeEntity = resourceTypeService.getResourceTypeById(resourceTypeId);
            if (resourceTypeEntity == null) {
                return ValidationResult.invalid("资源类型不存在: " + resourceTypeId);
            }

            // 获取资源提供者
            ResourceProvider<? extends Resource> provider = resourceProviderFactory.getProvider(resourceTypeId);
            if (provider == null) {
                return ValidationResult.invalid("找不到资源提供者: " + resourceTypeId);
            }

            // 验证配置
            return provider.validateConfig(config);
        } catch (Exception e) {
            log.error("验证资源配置失败", e);
            return ValidationResult.invalid("验证配置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isResourceInstantiated(String resourceConfigId) {
        try {
            // 检查资源是否已在注册表中
            return resourceRegistry.contains(resourceConfigId);
        } catch (Exception e) {
            log.error("检查资源实例化状态失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> batchCheckResourceInstantiated(List<String> resourceConfigIds) {
        Map<String, Boolean> result = new HashMap<>();

        if (resourceConfigIds == null || resourceConfigIds.isEmpty()) {
            return result;
        }

        try {
            // 批量检查资源是否已在注册表中
            for (String resourceConfigId : resourceConfigIds) {
                try {
                    boolean instantiated = resourceRegistry.contains(resourceConfigId);
                    result.put(resourceConfigId, instantiated);
                } catch (Exception e) {
                    log.error("检查资源实例化状态失败: {}", resourceConfigId, e);
                    result.put(resourceConfigId, false);
                }
            }
        } catch (Exception e) {
            log.error("批量检查资源实例化状态失败", e);
            // 如果批量检查失败，将所有ID标记为未实例化
            for (String resourceConfigId : resourceConfigIds) {
                result.put(resourceConfigId, false);
            }
        }

        return result;
    }

    @Override
    public ResourceInstantiationResult instantiateResource(String resourceConfigId) {
        try {
            // 检查资源是否已实例化
            if (resourceRegistry.contains(resourceConfigId)) {
                Resource resource = resourceRegistry.get(resourceConfigId);
                if (resource != null) {
                    return ResourceInstantiationResult.success(
                        resource.getId(),
                        resource.getType(),
                        resource.getName(),
                        resource.getDescription(),
                        resource.getStatus()
                    );
                }
            }

            // 创建资源实例
            Resource resource = resourceLifecycleManager.createResource(resourceConfigId);
            return ResourceInstantiationResult.success(
                resource.getId(),
                resource.getType(),
                resource.getName(),
                resource.getDescription(),
                resource.getStatus()
            );
        } catch (MiddlewareBusinessException e) {
            log.error("实例化资源失败: {}", resourceConfigId, e);
            return ResourceInstantiationResult.failure(resourceConfigId, e.getMessage());
        } catch (Exception e) {
            log.error("实例化资源失败: {}", resourceConfigId, e);
            return ResourceInstantiationResult.failure(resourceConfigId, "实例化资源失败: " + e.getMessage());
        }
    }

    @Override
    public boolean destroyResource(String resourceConfigId) {
        try {
            // 检查资源是否已实例化
            if (!resourceRegistry.contains(resourceConfigId)) {
                log.warn("资源未实例化，无需销毁: {}", resourceConfigId);
                return true;
            }

            // 获取资源实例
            Resource resource = resourceRegistry.get(resourceConfigId);
            if (resource == null) {
                log.warn("资源不存在，无需销毁: {}", resourceConfigId);
                return true;
            }

            // 销毁资源实例
            resourceLifecycleManager.destroyResource(resource);
            return true;
        } catch (Exception e) {
            log.error("销毁资源失败: {}", resourceConfigId, e);
            return false;
        }
    }

    @Override
    public HealthStatus checkResourceHealth(String resourceConfigId) {
        try {
            // 检查资源是否已实例化
            if (!resourceRegistry.contains(resourceConfigId)) {
                return HealthStatus.down("资源未实例化");
            }

            // 获取资源实例
            Resource resource = resourceRegistry.get(resourceConfigId);
            if (resource == null) {
                return HealthStatus.down("资源不存在");
            }

            // 检查资源健康状态
            return resourceLifecycleManager.checkResourceHealth(resource);
        } catch (Exception e) {
            log.error("检查资源健康状态失败: {}", resourceConfigId, e);
            return HealthStatus.down("检查健康状态失败: " + e.getMessage());
        }
    }
}

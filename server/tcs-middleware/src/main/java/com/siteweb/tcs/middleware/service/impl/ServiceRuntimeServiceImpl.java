package com.siteweb.tcs.middleware.service.impl;

import com.siteweb.tcs.middleware.common.exception.MiddlewareBusinessException;
import com.siteweb.tcs.middleware.common.lifecycle.ServiceLifecycleManager;
import com.siteweb.tcs.middleware.common.model.ConnectionTestResult;
import com.siteweb.tcs.middleware.common.model.HealthStatus;
import com.siteweb.tcs.middleware.dto.ServiceInstantiationResult;
import com.siteweb.tcs.middleware.common.model.ValidationResult;
import com.siteweb.tcs.middleware.common.registry.ResourceRegistry;
import com.siteweb.tcs.middleware.common.registry.ServiceRegistry;
import com.siteweb.tcs.middleware.common.service.Service;
import com.siteweb.tcs.middleware.common.service.ServiceProvider;
import com.siteweb.tcs.middleware.common.service.ServiceProviderFactory;
import com.siteweb.tcs.middleware.entity.ServiceTypeEntity;
import com.siteweb.tcs.middleware.service.ResourceRuntimeService;
import com.siteweb.tcs.middleware.service.ServiceConfigurationService;
import com.siteweb.tcs.middleware.service.ServiceRuntimeService;
import com.siteweb.tcs.middleware.service.ServiceTypeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 服务运行时服务实现类
 */
@Slf4j
@org.springframework.stereotype.Service
public class ServiceRuntimeServiceImpl implements ServiceRuntimeService {

    @Autowired
    private ServiceProviderFactory serviceProviderFactory;

    @Autowired
    private ServiceRegistry serviceRegistry;

    @Autowired
    private ResourceRegistry resourceRegistry;

    @Autowired
    private ServiceLifecycleManager serviceLifecycleManager;

    @Autowired
    private ServiceConfigurationService serviceConfigurationService;

    @Autowired
    private ServiceTypeService serviceTypeService;

    @Autowired
    private ResourceRuntimeService resourceRuntimeService;

    @Override
    public ConnectionTestResult testConnection(String serviceTypeId, Map<String, Object> config) {
        try {
            // 获取服务类型
            ServiceTypeEntity serviceTypeEntity = serviceTypeService.getServiceTypeById(serviceTypeId);
            if (serviceTypeEntity == null) {
                return new ConnectionTestResult(false, "服务类型不存在: " + serviceTypeId);
            }

            // 获取服务提供者
            ServiceProvider<? extends Service> provider = serviceProviderFactory.getProvider(serviceTypeId);
            if (provider == null) {
                return new ConnectionTestResult(false, "找不到服务提供者: " + serviceTypeId);
            }

            // 测试连接
            return provider.testConnection(config);
        } catch (Exception e) {
            log.error("测试服务连接失败", e);
            return new ConnectionTestResult(false, "测试连接失败: " + e.getMessage());
        }
    }

    @Override
    public ValidationResult validateConfig(String serviceTypeId, Map<String, Object> config) {
        try {
            // 获取服务类型
            ServiceTypeEntity serviceTypeEntity = serviceTypeService.getServiceTypeById(serviceTypeId);
            if (serviceTypeEntity == null) {
                return ValidationResult.invalid("服务类型不存在: " + serviceTypeId);
            }

            // 获取服务提供者
            ServiceProvider<? extends Service> provider = serviceProviderFactory.getProvider(serviceTypeId);
            if (provider == null) {
                return ValidationResult.invalid("找不到服务提供者: " + serviceTypeId);
            }

            // 验证配置
            return provider.validateConfig(config);
        } catch (Exception e) {
            log.error("验证服务配置失败", e);
            return ValidationResult.invalid("验证配置失败: " + e.getMessage());
        }
    }

    @Override
    public boolean isServiceInstantiated(String serviceConfigId) {
        try {
            // 检查服务是否已在注册表中
            return serviceRegistry.contains(serviceConfigId);
        } catch (Exception e) {
            log.error("检查服务实例化状态失败", e);
            return false;
        }
    }

    @Override
    public Map<String, Boolean> batchCheckServiceInstantiated(List<String> serviceConfigIds) {
        Map<String, Boolean> result = new HashMap<>();

        if (serviceConfigIds == null || serviceConfigIds.isEmpty()) {
            return result;
        }

        try {
            // 批量检查服务是否已在注册表中
            for (String serviceConfigId : serviceConfigIds) {
                try {
                    boolean instantiated = serviceRegistry.contains(serviceConfigId);
                    result.put(serviceConfigId, instantiated);
                } catch (Exception e) {
                    log.error("检查服务实例化状态失败: {}", serviceConfigId, e);
                    result.put(serviceConfigId, false);
                }
            }
        } catch (Exception e) {
            log.error("批量检查服务实例化状态失败", e);
            // 如果批量检查失败，将所有ID标记为未实例化
            for (String serviceConfigId : serviceConfigIds) {
                result.put(serviceConfigId, false);
            }
        }

        return result;
    }

    @Override
    public ServiceInstantiationResult instantiateService(String serviceConfigId) {
        try {
            // 检查服务是否已实例化
            if (serviceRegistry.contains(serviceConfigId)) {
                Service service = serviceRegistry.get(serviceConfigId);
                if (service != null) {
                    String resourceId = null;
                    if (service.getResource() != null) {
                        resourceId = service.getResource().getId();
                    }
                    return ServiceInstantiationResult.success(
                        service.getId(),
                        service.getType(),
                        service.getName(),
                        service.getDescription(),
                        service.getStatus(),
                        resourceId
                    );
                }
            }

            // 创建服务实例
            Service service = serviceLifecycleManager.createService(serviceConfigId);
            String resourceId = null;
            if (service.getResource() != null) {
                resourceId = service.getResource().getId();
            }
            return ServiceInstantiationResult.success(
                service.getId(),
                service.getType(),
                service.getName(),
                service.getDescription(),
                service.getStatus(),
                resourceId
            );
        } catch (MiddlewareBusinessException e) {
            log.error("实例化服务失败: {}", serviceConfigId, e);
            return ServiceInstantiationResult.failure(serviceConfigId, e.getMessage());
        } catch (Exception e) {
            log.error("实例化服务失败: {}", serviceConfigId, e);
            return ServiceInstantiationResult.failure(serviceConfigId, "实例化服务失败: " + e.getMessage());
        }
    }

    @Override
    public boolean destroyService(String serviceConfigId) {
        try {
            // 检查服务是否已实例化
            if (!serviceRegistry.contains(serviceConfigId)) {
                log.warn("服务未实例化，无需销毁: {}", serviceConfigId);
                return true;
            }

            // 获取服务实例
            Service service = serviceRegistry.get(serviceConfigId);
            if (service == null) {
                log.warn("服务不存在，无需销毁: {}", serviceConfigId);
                return true;
            }

            // 销毁服务实例
            serviceLifecycleManager.destroyService(service);
            return true;
        } catch (Exception e) {
            log.error("销毁服务失败: {}", serviceConfigId, e);
            return false;
        }
    }

    @Override
    public HealthStatus checkServiceHealth(String serviceConfigId) {
        try {
            // 检查服务是否已实例化
            if (!serviceRegistry.contains(serviceConfigId)) {
                return HealthStatus.down("服务未实例化: " + serviceConfigId);
            }

            // 获取服务实例
            Service service = serviceRegistry.get(serviceConfigId);
            if (service == null) {
                return HealthStatus.down("服务不存在: " + serviceConfigId);
            }

            // 检查服务健康状态
            return service.checkHealth();
        } catch (Exception e) {
            log.error("检查服务健康状态失败: {}", serviceConfigId, e);
            return HealthStatus.down("检查健康状态失败: " + e.getMessage());
        }
    }
}

@echo off
REM FSU生命周期事件测试脚本

echo Testing FSU Lifecycle Events...

set BASE_URL=http://localhost:8080
set API_BASE=%BASE_URL%/api/cmcc/2016/fsu

echo.
echo === Testing Single FSU Lifecycle Events ===

echo Testing CREATE event for FSU001...
curl -X POST "%API_BASE%/trigger-lifecycle/FSU001/CREATE"
echo.

echo Testing LOAD event for FSU001...
curl -X POST "%API_BASE%/trigger-lifecycle/FSU001/LOAD"
echo.

echo Testing START event for FSU001...
curl -X POST "%API_BASE%/trigger-lifecycle/FSU001/START"
echo.

echo Testing STOP event for FSU001...
curl -X POST "%API_BASE%/trigger-lifecycle/FSU001/STOP"
echo.

echo.
echo === Testing Batch FSU Lifecycle Events ===

echo Testing batch LOAD event...
curl -X POST "%API_BASE%/trigger-lifecycle-batch" ^
  -H "Content-Type: application/json" ^
  -d "{\"fsuIds\":[\"FSU001\",\"FSU002\"],\"eventType\":\"LOAD\"}"
echo.

echo Testing batch START event...
curl -X POST "%API_BASE%/trigger-lifecycle-batch" ^
  -H "Content-Type: application/json" ^
  -d "{\"fsuIds\":[\"FSU001\",\"FSU002\"],\"eventType\":\"START\"}"
echo.

echo.
echo === Testing Error Cases ===

echo Testing invalid FSU...
curl -X POST "%API_BASE%/trigger-lifecycle/FSU999/CREATE"
echo.

echo Testing invalid event type...
curl -X POST "%API_BASE%/trigger-lifecycle/FSU001/INVALID_EVENT"
echo.

echo.
echo Testing completed! Check the server logs for event processing details.
pause

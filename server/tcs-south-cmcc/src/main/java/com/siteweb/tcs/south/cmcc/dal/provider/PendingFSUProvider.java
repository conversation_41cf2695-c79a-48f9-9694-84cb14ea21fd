package com.siteweb.tcs.south.cmcc.dal.provider;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.south.cmcc.dal.entity.CMCCPendingFsu;
import com.siteweb.tcs.south.cmcc.dal.mapper.CMCCPendingFSUMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
/**
 * <AUTHOR> (2025-05-15)
 **/
@Slf4j
@Service
public class PendingFSUProvider {
    @Autowired
    private CMCCPendingFSUMapper paddingFSUMapper;
    // 创建一个线程池用于异步操作
    private final Executor executor = Executors.newFixedThreadPool(10);
    public List<CMCCPendingFsu> getPendingList() {
        try {
            LambdaQueryWrapper<CMCCPendingFsu> wrapper = new LambdaQueryWrapper<>();
            return paddingFSUMapper.selectList(wrapper);
        } catch (Exception e) {
            return List.of();
        }
    }
    public CMCCPendingFsu getFsuByFsuid(String fsuid) {
        try {
            LambdaQueryWrapper<CMCCPendingFsu> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CMCCPendingFsu::getFsuID, fsuid);
            return paddingFSUMapper.selectOne(wrapper);
        } catch (Exception e) {
            log.error("Error getting FSU by FSUID: {}", fsuid, e);
            return null;
        }
    }
    /**
     * 保存FSU信息
     *
     * @param fsuInfo FSU信息
     * @return 是否成功
     */
    public boolean save(CMCCPendingFsu fsuInfo) {
        try {
            fsuInfo.setCreateTime(LocalDateTime.now());
            return paddingFSUMapper.insert(fsuInfo) > 0;
        } catch (Exception e) {
            log.error("Error saving FSU: {}", fsuInfo.getFsuID(), e);
            return false;
        }
    }
}
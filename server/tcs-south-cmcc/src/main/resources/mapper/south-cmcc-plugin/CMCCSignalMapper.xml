<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cmcc.dal.mapper.CMCCSignalMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.cmcc.dal.entity.CMCCSignal">
        <id column="id" property="id" />
        <result column="fsu_id" property="fsuId" />
        <result column="device_id" property="deviceId" />
        <result column="sp_id" property="spId" />
        <result column="sp_name" property="spName" />
        <result column="sp_type" property="spType" />
        <result column="alarm_meanings" property="alarmMeanings" />
        <result column="normal_meanings" property="normalMeanings" />
        <result column="unit" property="unit" />
        <result column="nm_alarm_id" property="NMAlarmID" />
        <result column="alarm_level" property="alarmLevel" />
        <result column="device_hl_type" property="deviceHLType" />
        <result column="meanings" property="meanings" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fsu_id, device_id, sp_id, sp_name, sp_type, alarm_meanings, normal_meanings, unit, nm_alarm_id, alarm_level, device_hl_type, meanings
    </sql>

</mapper>

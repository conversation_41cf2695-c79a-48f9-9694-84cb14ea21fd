<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.south.cmcc.dal.mapper.CMCCDeviceMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.south.cmcc.dal.entity.CMCCDevice">
        <id column="id" property="id" />
        <result column="fsu_id" property="fsuID" />
        <result column="device_id" property="deviceID" />
        <result column="device_name" property="deviceName" />
        <result column="site_name" property="siteName" />
        <result column="room_name" property="roomName" />
        <result column="device_type" property="deviceType" />
        <result column="device_sub_type" property="deviceSubType" />
        <result column="model" property="model" />
        <result column="brand" property="brand" />
        <result column="rated_capacity" property="ratedCapacity" />
        <result column="version" property="version" />
        <result column="begin_run_time" property="beginRunTime" />
        <result column="dev_describe" property="devDescribe" />
        <result column="description" property="description" />
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id, fsu_id, device_id, device_name, site_name, room_name, device_type, device_sub_type, model, brand, rated_capacity, version, begin_run_time, dev_describe, description
    </sql>

</mapper>

package com.siteweb.tcs.cmcc.common.message;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlRootElement;
import com.siteweb.tcs.cmcc.common.protocol.EnumResult;
import com.siteweb.tcs.cmcc.common.protocol.PK_TypeName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.pekko.actor.ActorRef;


/**
 * 移动B接口主动请求报文
 *
 * <AUTHOR> (2025-05-09)
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@JacksonXmlRootElement(localName = "Request")
public class MobileBRequestMessage extends MobileBMessage {

    public MobileBRequestMessage() {
    }

    public MobileBRequestMessage(PK_TypeName pkType) {
        getPkType().setName(pkType);
    }


    /**
     * 响应Actor
     */
    @JsonIgnore
    private ActorRef responseActor;


    public void responseFail(String failureCause) {
        var type = this.getPkType().getName();
        if (type.getAck() != null && responseActor != null) {
            var response = new MobileBResponseMessage(type.getAck());
            response.getInfo().setResult(EnumResult.FAILURE);
            response.getInfo().setFailureCause(failureCause);
            responseActor.tell(response, ActorRef.noSender());
        }
    }

    public void responseSuccess() {
        var type = this.getPkType().getName();
        if (type.getAck() != null && responseActor != null) {
            var response = new MobileBResponseMessage(this.getPkType().getName().getAck());
            response.getInfo().setResult(EnumResult.SUCCESS);
            responseActor.tell(response, ActorRef.noSender());
        }
    }

    public void response(MobileBResponseMessage responseMessage) {
        if (responseActor != null) {
            responseActor.tell(responseMessage, ActorRef.noSender());
        }
    }


}

package com.siteweb.stream.common.exception;

import com.siteweb.tcs.common.exception.code.TechnicalErrorCode;
import lombok.Getter;

/**
 * <AUTHOR> (2025-04-24)
 **/
public enum StreamException implements TechnicalErrorCode {


    STREAM_MODULE_NOT_FOUND("STREAM_MODULE_NOT_FOUND", "无效的Stream模块，未找到。"), //
    STREAM_MODULE_HAS_BEEN_LOADED("STREAM_MODULE_HAS_BEEN_LOADED", "Stream模块已加载。"), //
    STREAM_MODULE_JAR_FILE_HAS_BEEN_TEMPERED("STREAM_MODULE_HAS_BEEN_TEMPERED", "Stream模块jar文件被篡改。"), //




    ;

    StreamException(String code, String message) {
        this.code = code;
        this.message = message;
    }


    @Getter
    private String code;


    @Getter
    private String message;


}

package com.siteweb.stream.common.runtime;

import lombok.AllArgsConstructor;
import org.apache.pekko.actor.Actor;
import org.apache.pekko.actor.Props;
import org.springframework.context.ApplicationContext;

/**
 * 支持Spring boot注入的 Props
 *
 * <AUTHOR> (2025-02-14)
 **/
@AllArgsConstructor
public class SpringProps {
    private final ApplicationContext applicationContext;

    /**
     * 创建无参的Actor Props
     *
     * @param actorClass actor
     * @param <T>
     * @return
     */
    public <T extends Actor> Props props(Class<T> actorClass) {
        return Props.create(actorClass, () -> {
            var constructor = actorClass.getConstructor();
            var instance = constructor.newInstance();
            applicationContext.getAutowireCapableBeanFactory().autowireBean(instance);
            return instance;
        });
    }


    /**
     * 未实现的
     *
     * @param actorClass
     * @param objects
     * @param <T>
     * @return
     */
    public <T extends Actor> Props props(Class<T> actorClass, Object... objects) {
        return Props.create(actorClass, () -> {
            var constructor = actorClass.getConstructor();
            var instance = constructor.newInstance();
            applicationContext.getAutowireCapableBeanFactory().autowireBean(instance);
            return instance;
        });
    }


}


@echo off
REM 测试ResourceConfigController的启用和禁用功能

echo Testing ResourceConfigController enable/disable functionality...

set BASE_URL=http://localhost:8080
set API_BASE=%BASE_URL%/middleware/resource-configs

echo.
echo === Testing Resource Configuration Enable/Disable ===

echo.
echo 1. 获取资源配置列表...
curl -X GET "%API_BASE%/list"
echo.

echo.
echo 2. 创建测试资源配置...
curl -X POST "%API_BASE%" ^
  -H "Content-Type: application/json" ^
  -d "{\"id\":\"test-resource-001\",\"resourceId\":\"database\",\"name\":\"测试数据库配置\",\"description\":\"用于测试启用禁用功能\",\"config\":{\"host\":\"localhost\",\"port\":3306},\"status\":\"DISABLED\"}"
echo.

echo.
echo 3. 启用资源配置...
curl -X POST "%API_BASE%/enable/test-resource-001"
echo.

echo.
echo 4. 查看启用后的状态...
curl -X GET "%API_BASE%/test-resource-001"
echo.

echo.
echo 5. 禁用资源配置...
curl -X POST "%API_BASE%/disable/test-resource-001"
echo.

echo.
echo 6. 查看禁用后的状态...
curl -X GET "%API_BASE%/test-resource-001"
echo.

echo.
echo 7. 测试不存在的资源配置...
curl -X POST "%API_BASE%/enable/non-existent-id"
echo.

echo.
echo 8. 清理测试数据...
curl -X DELETE "%API_BASE%/test-resource-001"
echo.

echo.
echo Testing completed! Check the responses above for:
echo   - Status changes from DISABLED to ENABLED and back
echo   - Error handling for non-existent resources
echo   - Proper JSON responses

pause

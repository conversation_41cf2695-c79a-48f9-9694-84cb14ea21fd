package com.siteweb.tcs.tracking.dal.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * 埋点数据聚合实体类
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName("tcs_tracking_aggregation")
public class TrackingAggregation {
    
    /**
     * 聚合ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;
    
    /**
     * 埋点点位ID
     */
    private Integer pointId;
    
    /**
     * 策略ID
     */
    private Integer strategyId;
    
    /**
     * 聚合类型：COUNT/SUM/AVG/MIN/MAX
     */
    private String aggregationType;
    
    /**
     * 时间窗口：MINUTE/HOUR/DAY/WEEK/MONTH
     */
    private String timeWindow;
    
    /**
     * 窗口开始时间
     */
    private LocalDateTime windowStart;
    
    /**
     * 窗口结束时间
     */
    private LocalDateTime windowEnd;
    
    /**
     * 聚合维度
     */
    private String dimension;
    
    /**
     * 维度值
     */
    private String dimensionValue;
    
    /**
     * 聚合值
     */
    private Double value;
    
    /**
     * 样本数量
     */
    private Integer sampleCount;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 关联的埋点点位（非数据库字段）
     */
    @TableField(exist = false)
    private TrackingPoint trackingPoint;
    
    /**
     * 关联的埋点策略（非数据库字段）
     */
    @TableField(exist = false)
    private TrackingStrategy trackingStrategy;
    
    /**
     * 聚合类型枚举
     */
    public enum AggregationType {
        COUNT,  // 计数
        SUM,    // 求和
        AVG,    // 平均值
        MIN,    // 最小值
        MAX     // 最大值
    }
    
    /**
     * 时间窗口枚举
     */
    public enum TimeWindow {
        MINUTE, // 分钟
        HOUR,   // 小时
        DAY,    // 天
        WEEK,   // 周
        MONTH   // 月
    }
}

package com.siteweb.tcs.tracking;

import com.siteweb.tcs.common.runtime.NorthPlugin;
import com.siteweb.tcs.common.runtime.PluginContext;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.tracking.connector.process.TrackingGuard;
import lombok.extern.slf4j.Slf4j;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 数据埋点系统插件
 * 负责管理和处理系统中的埋点数据
 */
@Slf4j
public class TrackingPlugin extends NorthPlugin {

    private ActorRef trackingGuard;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    /***
     * 插件构造函数
     * <AUTHOR> (2024/5/9)
     * @param context  插件上下文对象
     */
    protected TrackingPlugin(PluginContext context) {
        super(context);
    }

    /**
     * 插件启动时执行
     */
    @Override
    protected void onStart() {
        log.info("Starting Tracking Plugin...");

        // 创建根Actor
        trackingGuard = ClusterContext.getActorSystem().actorOf(
                Props.create(TrackingGuard.class, redisTemplate),
                "TrackingGuard"
        );

        // 注册根Actor
        getConnectorDataHolder().registerNorthEntry(trackingGuard);

        log.info("Tracking Plugin started successfully");
    }

    /**
     * 插件停止时执行
     */
    @Override
    protected void onStop() {
        log.info("Stopping Tracking Plugin...");

        // 停止根Actor
        if (trackingGuard != null) {
            ClusterContext.getActorSystem().stop(trackingGuard);
            trackingGuard = null;
        }

        log.info("Tracking Plugin stopped successfully");
    }
}

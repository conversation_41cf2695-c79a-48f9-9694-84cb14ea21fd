package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.ConfigChangeMacroLog;
import com.siteweb.tcs.siteweb.enums.ChangeOperatorEnum;
import com.siteweb.tcs.siteweb.mapper.ConfigChangeMacroLogMapper;
import com.siteweb.tcs.siteweb.service.IConfigChangeMacroLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * Config Change Macro Log Service Implementation
 */
@Slf4j
@Service
public class ConfigChangeMacroLogServiceImpl extends ServiceImpl<ConfigChangeMacroLogMapper, ConfigChangeMacroLog> implements IConfigChangeMacroLogService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean configChangeLog(String objectId, Integer configId, ChangeOperatorEnum changeOperatorEnum) {
        if (objectId == null || configId == null || changeOperatorEnum == null) {
            log.warn("Cannot record config change log: parameters are null");
            return false;
        }

        try {
            ConfigChangeMacroLog log = new ConfigChangeMacroLog();
            log.setObjectId(objectId);
            log.setConfigId(configId);
            log.setEditType(changeOperatorEnum.getValue());
            log.setUpdateTime(LocalDateTime.now());

            return save(log);
        } catch (Exception e) {
            log.error("Error recording config change log: {}", e.getMessage(), e);
            return false;
        }
    }
}

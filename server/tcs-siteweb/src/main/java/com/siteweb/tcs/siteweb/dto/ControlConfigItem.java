package com.siteweb.tcs.siteweb.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.siteweb.tcs.siteweb.entity.ControlMeanings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 控制配置项DTO
 */
@AllArgsConstructor
@NoArgsConstructor
@Data
public class ControlConfigItem {
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer controlId;
    @Size(max = 128, message = "控制名称长度不能超过128")
    private String controlName;
    private Integer controlCategory;
    private String cmdToken;
    private Long baseTypeId;
    private String baseTypeName;
    private Integer controlSeverity;
    private Integer signalId;
    private Double timeOut;
    private Integer retry;
    private String description;
    private Boolean enable;
    private Boolean visible;
    private Integer displayIndex;
    private Integer commandType;
    private Short controlType;
    private Short dataType;
    private Double maxValue;
    private Double minValue;
    private Double defaultValue;
    private Integer moduleNo;
    private Integer baseStatusId;
    private String baseNameExt;
    private List<ControlMeanings> controlMeaningsList;

    @JsonIgnore
    public String getControlKey() {
        return this.equipmentTemplateId + "." + this.controlId;
    }

    @JsonIgnore
    public List<ControlMeanings> getNotEmptyControlMeaningsList() {
        if (CollectionUtils.isEmpty(this.controlMeaningsList)) {
            return List.of(new ControlMeanings());
        }
        return controlMeaningsList;
    }
} 
package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.TslMonitorUnitSignal;

/**
 * 监控单元信号服务接口
 */
public interface ITslMonitorUnitSignalService extends IService<TslMonitorUnitSignal> {
    
    /**
     * 根据站点ID删除监控单元信号
     *
     * @param stationId 站点ID
     * @return 是否删除成功
     */
    boolean deleteByStationId(Integer stationId);
} 
package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.Door;
import com.siteweb.tcs.siteweb.service.IDoorCardService;
import com.siteweb.tcs.siteweb.service.IDoorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 门禁数据变更处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class DoorChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IDoorService doorService;
    
    @Autowired
    private IDoorCardService doorCardService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(Door.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        Door door = changeRecord.readMessageBody(Door.class);
        log.info("门禁创建成功,doorId:{},doorName:{},equipmentId:{}", 
                door.getDoorId(), door.getDoorName(), door.getEquipmentId());
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        Door door = changeRecord.readMessageBody(Door.class);
        log.info("门禁删除成功,doorId:{},doorName:{},equipmentId:{}", 
                door.getDoorId(), door.getDoorName(), door.getEquipmentId());
        
        // 删除门禁卡
        doorCardService.deleteByEquipmentId(door.getEquipmentId());
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        Door door = changeRecord.readMessageBody(Door.class);
        log.trace("门禁修改成功,doorId:{},doorName:{},equipmentId:{}", 
                door.getDoorId(), door.getDoorName(), door.getEquipmentId());
    }
}

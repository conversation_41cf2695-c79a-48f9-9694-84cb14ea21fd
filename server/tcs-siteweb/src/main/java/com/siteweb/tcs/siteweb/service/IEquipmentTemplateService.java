package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.EquipmentTemplate;
import com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO;

import java.util.List;

/**
 * Equipment Template Service Interface
 */
public interface IEquipmentTemplateService extends IService<EquipmentTemplate> {

    /**
     * 获取B接口设备根模板ID
     * @return B接口设备根模板ID，如果找不到则返回null
     */
    Integer getBInterfaceDeviceTemplateRootId();

    /**
     * 根据设备模板ID删除模板及其关联数据
     * @param equipmentTemplateId 设备模板ID
     * @return 删除成功返回true，否则返回false
     */
    boolean deleteTemplateById(Integer equipmentTemplateId);

    /**
     * 创建设备模板
     * @param equipmentTemplate 设备模板实体
     * @return 创建后的设备模板实体（包含生成的ID）
     */
    EquipmentTemplate createEquipmentTemplate(EquipmentTemplate equipmentTemplate);

    /**
     * 根据查询条件查询设备模板
     * @param equipmentTemplateVO 查询条件VO
     * @return 符合条件的设备模板列表
     */
    List<EquipmentTemplate> queryTemplate(EquipmentTemplateVO equipmentTemplateVO);

    /**
     * 判断是否存在子模板
     * @param equipmentTemplateId 父模板ID
     * @return 存在子模板返回true，否则返回false
     */
    boolean existsChildTemplate(Integer equipmentTemplateId);

    EquipmentTemplate findById(Integer equipmentTemplateId);

    /**
     * 根据分类映射更新设备模板分类
     *
     * @param businessId 业务ID
     * @param categoryTypeId 分类类型ID
     * @return 是否更新成功
     */
    boolean updateEquipmentTemplateCategoryByCategoryIdMap(Integer businessId, Integer categoryTypeId);

    /**
     * 更新设备模板的基础类型为空
     *
     * @return 是否更新成功
     */
    boolean updateEquipmentBaseTypeToNull();

    /**
     * 根据设备模板分类查询设备模板ID
     *
     * @param equipmentTemplateIdDiv 设备模板分类
     * @return 设备模板ID列表
     */
    List<Integer> findEquipmentTemplateIdByEquipmentTemplateIdDiv(Integer equipmentTemplateIdDiv);

    /**
     * 批量保存联通标准的设备模板
     *
     * @return 是否保存成功
     */
    boolean batchsaveLianTongEquipmentTemplate();
}

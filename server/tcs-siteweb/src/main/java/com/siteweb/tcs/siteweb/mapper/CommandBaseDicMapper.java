package com.siteweb.tcs.siteweb.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.siteweb.tcs.siteweb.entity.CommandBaseDic;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * Command Base Dictionary Mapper
 */
@Mapper
@Repository
public interface CommandBaseDicMapper extends BaseMapper<CommandBaseDic> {

    /**
     * 生成控制基类字典
     * @param baseTypeId 目标基类ID
     * @param sourceId 源基类ID
     */
    void generateCommandBaseDic(@Param("baseTypeId") Long baseTypeId, @Param("sourceId") Long sourceId);
}

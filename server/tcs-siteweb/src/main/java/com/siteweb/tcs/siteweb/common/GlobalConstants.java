package com.siteweb.tcs.siteweb.common;

public final class GlobalConstants {

    private GlobalConstants() {
        // Private constructor to prevent instantiation
    }

    /**
     * Default postal code entry ID, used when a specific postal code is not provided
     * and a default needs to be looked up (e.g., in tbl_dataitem).
     */
    public static final String DEFAULT_POSTAL_CODE_ENTRYID = "DEFAULT_POSTAL_CODE"; // Placeholder value, adjust as needed

    /**
     * Base value for ID generation when a new record is created in tbl_primarykeyvalue.
     */
    public static final Integer ID_GENERATION_BASE = 1; // Default base value for ID generation

}
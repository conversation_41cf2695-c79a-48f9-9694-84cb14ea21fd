package com.siteweb.tcs.siteweb.util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.experimental.UtilityClass;

@UtilityClass
public class StrSplitUtil {
    public List<Integer> splitToIntList(String str) {
        return splitToIntList(str, ",");
    }

    public List<Integer> splitToIntList(String str, String separator) {
        return splitToList(str, separator, Integer::valueOf);
    }

    public <E> List<E> splitToList(String str, Function<String, E> converter) {
        return splitToList(str, ",", converter);
    }

    public <E> List<E> splitToList(String str, String separator, Function<String, E> converter) {
        return splitToCollection(str, separator, converter, ArrayList::new);
    }

    public <E, T extends Collection<E>> T splitToCollection(String str, Function<String, E> converter, Supplier<T> collectionFactory) {
        return splitToCollection(str, ",", converter, collectionFactory);
    }

    public <E, T extends Collection<E>> T splitToCollection(String str, String separator, Function<String, E> converter, Supplier<T> collectionFactory) {
        if (CharSequenceUtil.isBlank(str)) {
            return collectionFactory.get();
        }
        return Arrays.stream(str.split(separator))
                     .filter(CharSequenceUtil::isNotBlank)
                     .map(converter)
                     .collect(Collectors.toCollection(collectionFactory));
    }
} 
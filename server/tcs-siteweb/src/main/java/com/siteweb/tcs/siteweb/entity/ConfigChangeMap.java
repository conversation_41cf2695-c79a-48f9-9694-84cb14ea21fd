package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Config change map entity
 */
@Data
@TableName("tbl_configchangemap")
public class ConfigChangeMap implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableField("MicroConfigId")
    private Integer microConfigId;

    @TableField("MicroEditType")
    private Integer microEditType;

    @TableField("MacroConfigId")
    private Integer macroConfigId;

    @TableField("MacroEditType")
    private Integer macroEditType;

    @TableField("IdConvertRule")
    private String idConvertRule;
}

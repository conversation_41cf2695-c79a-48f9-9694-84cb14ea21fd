package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.common.GlobalConstants;
import com.siteweb.tcs.siteweb.entity.PrimaryKeyValue;
import com.siteweb.tcs.siteweb.entity.PrimaryKeyIdentity;
import com.siteweb.tcs.siteweb.entity.DataItem;
import com.siteweb.tcs.siteweb.enums.DataEntryEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.mapper.PrimaryKeyValueMapper;
import com.siteweb.tcs.siteweb.mapper.PrimaryKeyIdentityMapper;
import com.siteweb.tcs.siteweb.mapper.DataItemMapper;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Primary Key Value Service Implementation
 */
@Slf4j
@Service
public class PrimaryKeyValueServiceImpl extends ServiceImpl<PrimaryKeyValueMapper, PrimaryKeyValue> implements IPrimaryKeyValueService {

    @Autowired
    private PrimaryKeyValueMapper primaryKeyValueMapper;

    @Autowired
    private PrimaryKeyIdentityMapper primaryKeyIdentityMapper;

    @Autowired
    private DataItemMapper dataItemMapper;

    // 简单的内存计数器，实际项目中应该使用数据库或Redis
    private final AtomicInteger counter = new AtomicInteger(1000);

    /**
     * 获取主键id
     *
     * @param tableIdentityEnum 表标识枚举
     * @param postalCode        邮政编码
     * @return 获取主键id，失败返回负数
     */
    @Override
    @Transactional
    public int getGlobalIdentity(TableIdentityEnum tableIdentityEnum, int postalCode) {
        // 参数校验
        if (CharSequenceUtil.isBlank(tableIdentityEnum.getTableName())) {
            return -1;
        }

        // 获取邮政编码
        int finalPostalCode = resolvePostalCode(postalCode, tableIdentityEnum);

        // 获取表主键标识
        PrimaryKeyIdentity primaryKeyIdentity = getPrimaryKeyIdentity(tableIdentityEnum);
        if (primaryKeyIdentity == null) {
            return -2;
        }

        // 获取或创建主键值
        PrimaryKeyValue primaryKeyValue = getOrCreatePrimaryKeyValue(primaryKeyIdentity, finalPostalCode);

        // 计算全局唯一标识
        return calculateGlobalIdentity(finalPostalCode, primaryKeyValue.getCurrentValue());
    }

    /**
     * 解析区划编码
     */
    private int resolvePostalCode(int postalCode, TableIdentityEnum tableIdentityEnum) {
        if ("tbl_dataitem".equalsIgnoreCase(tableIdentityEnum.getTableName())) {
            return 0;
        }

        if (postalCode == 0) {
            return Optional.ofNullable(dataItemMapper.selectOne(new QueryWrapper<DataItem>().lambda()
                    .eq(DataItem::getEntryId, DataEntryEnum.DATA_ENTRY.getValue())))
                    .map(DataItem::getItemId)
                    .orElse(0);
        }

        return postalCode;
    }

    /**
     * 获取表主键标识
     */
    private PrimaryKeyIdentity getPrimaryKeyIdentity(TableIdentityEnum tableIdentityEnum) {
        return primaryKeyIdentityMapper.selectOne(new QueryWrapper<PrimaryKeyIdentity>().lambda()
                .eq(PrimaryKeyIdentity::getTableName, tableIdentityEnum.getTableName()));
    }

    /**
     * 获取或创建主键值
     */
    private PrimaryKeyValue getOrCreatePrimaryKeyValue(PrimaryKeyIdentity primaryKeyIdentity, int postalCode) {
        PrimaryKeyValue primaryKeyValue = primaryKeyValueMapper.selectOne(new QueryWrapper<PrimaryKeyValue>().lambda()
                .eq(PrimaryKeyValue::getTableId, primaryKeyIdentity.getTableId())
                .eq(PrimaryKeyValue::getPostalCode, postalCode));

        if (primaryKeyValue == null) {
            return createNewPrimaryKeyValue(primaryKeyIdentity, postalCode);
        }

        return updateExistingPrimaryKeyValue(primaryKeyValue);
    }

    /**
     * 创建新的主键值记录
     */
    private PrimaryKeyValue createNewPrimaryKeyValue(PrimaryKeyIdentity primaryKeyIdentity, int postalCode) {
        PrimaryKeyValue newValue = new PrimaryKeyValue();
        newValue.setTableId(primaryKeyIdentity.getTableId());
        newValue.setPostalCode(postalCode);
        newValue.setMinValue(1);
        newValue.setCurrentValue(1);
        primaryKeyValueMapper.insert(newValue);
        return newValue;
    }

    /**
     * 更新现有主键值记录
     */
    private PrimaryKeyValue updateExistingPrimaryKeyValue(PrimaryKeyValue primaryKeyValue) {
        primaryKeyValue.setCurrentValue(primaryKeyValue.getCurrentValue() + 1);
        primaryKeyValueMapper.update(null, Wrappers.lambdaUpdate(PrimaryKeyValue.class)
                .set(PrimaryKeyValue::getCurrentValue, primaryKeyValue.getCurrentValue())
                .eq(PrimaryKeyValue::getTableId, primaryKeyValue.getTableId())
                .eq(PrimaryKeyValue::getPostalCode, primaryKeyValue.getPostalCode()));
        return primaryKeyValue;
    }

    /**
     * 计算全局唯一标识
     */
    private int calculateGlobalIdentity(int postalCode, int currentValue) {
        return postalCode * GlobalConstants.ID_GENERATION_BASE + currentValue;
    }

    @Override
    public void initPrimaryIdentityValue(int centerId, int defaultValue) {
        int tableIdSignal = findTableIdByName("TBL_Signal");
        int tableIdEvent = findTableIdByName("TBL_Event");
        int tableIdMonitorUnit = findTableIdByName("TSL_MonitorUnit");

        processTablePrimaryKey(tableIdSignal, centerId, defaultValue);
        processTablePrimaryKey(tableIdEvent, centerId, defaultValue);
        processTablePrimaryKey(tableIdMonitorUnit, centerId, defaultValue);
    }

    private int findTableIdByName(String tableName) {
        return primaryKeyIdentityMapper.selectOne(new QueryWrapper<PrimaryKeyIdentity>().eq("TableName", tableName)).getTableId();
    }

    private void processTablePrimaryKey(int tableId, int centerId, int defaultValue) {
        if (primaryKeyValueExists(tableId)) {
            updatePrimaryKeyValue(tableId, defaultValue);
        } else {
            insertPrimaryKeyValue(tableId, centerId, defaultValue);
        }
    }

    private boolean primaryKeyValueExists(int tableId) {
        return primaryKeyValueMapper.selectOne(new QueryWrapper<PrimaryKeyValue>().eq("TableId", tableId)) != null;
    }

    private void updatePrimaryKeyValue(int tableId, int defaultValue) {
        primaryKeyValueMapper.update(null, new UpdateWrapper<PrimaryKeyValue>()
                .eq("TableId", tableId)
                .set("CurrentValue", defaultValue));
    }

    private void insertPrimaryKeyValue(int tableId, int centerId, int defaultValue) {
        PrimaryKeyValue primaryKeyValue = new PrimaryKeyValue();
        primaryKeyValue.setCurrentValue(defaultValue);
        primaryKeyValue.setTableId(tableId);
        primaryKeyValue.setPostalCode(centerId);
        primaryKeyValue.setMinValue(1);
        primaryKeyValueMapper.insert(primaryKeyValue);
    }

    @Override
    public Integer getGlobalIdentity(TableIdentityEnum tableIdentityEnum, Integer centerId) {
        if (tableIdentityEnum == null) {
            log.warn("Cannot generate global identity: table identity enum is null");
            return null;
        }

        // 简单的ID生成策略，实际项目中应该根据具体需求实现
        int baseId = counter.incrementAndGet();
        
        // 根据表类型和中心ID生成不同的ID范围
        switch (tableIdentityEnum) {
            case TBL_STATION_STRUCTURE:
                return baseId + 10000;
            case TBL_STATION:
                return baseId + 20000;
            case TBL_DATA_ITEM:
                return baseId + 30000;
            default:
                return baseId;
        }
    }
}


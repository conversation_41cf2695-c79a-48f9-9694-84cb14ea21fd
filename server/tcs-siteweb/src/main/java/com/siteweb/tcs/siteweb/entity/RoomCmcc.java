package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 机房实体类 (CMCC)
 */
@Data
@TableName("tbl_roomcmcc")
public class RoomCmcc implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点ID (复合主键)
     */
    @TableField("StationId")
    private Integer stationId;

    /**
     * 机房ID (复合主键)
     */
    @TableField("HouseId")
    private Integer houseId;

    @TableField("RoomID")
    private String roomId;

    @TableField("RoomName")
    private String roomName;

    @TableField("SiteID")
    private String siteId;

    @TableField("Description")
    private String description;
}
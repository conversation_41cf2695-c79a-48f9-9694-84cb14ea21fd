package com.siteweb.tcs.siteweb.change;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.siteweb.tcs.siteweb.util.JsonHelper;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 变更消息类
 * 
 * <AUTHOR> (2024-02-22)
 **/
@Data
@NoArgsConstructor
public class ChangeMessage {

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime changeTime;

    private Object body;

    public ChangeMessage(Object data) {
        this.body = data;
        this.changeTime = LocalDateTime.now();
    }

    /**
     * 读取变更消息的Body数据
     *
     * @param tyleClass 对象类型的class 如 TBL_Equipment.class
     * <AUTHOR> (2024/3/9)
     */
    public <T> T readBody(Class<T> tyleClass) {
        return JsonHelper.convertTo(body, tyleClass);
    }
} 
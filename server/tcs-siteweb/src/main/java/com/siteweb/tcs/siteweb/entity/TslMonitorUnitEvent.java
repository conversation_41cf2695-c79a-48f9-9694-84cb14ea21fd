package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 监控单元事件实体类
 */
@Data
@TableName("tsl_monitorunitevent")
public class TslMonitorUnitEvent implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id")
    private Long id;

    @TableField("monitorUnitId")
    private Integer monitorUnitId;

    @TableField("stationId")
    private Integer stationId;

    @TableField("eventName")
    private String eventName;

    @TableField("eventCode")
    private String eventCode;

    @TableField("eventSeverity")
    private Integer eventSeverity;

    @TableField("description")
    private String description;
} 
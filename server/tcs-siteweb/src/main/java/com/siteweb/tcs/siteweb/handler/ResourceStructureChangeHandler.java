package com.siteweb.tcs.siteweb.handler;

import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.manager.StructureTreeManager;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * ResourceStructure 变更事件处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class ResourceStructureChangeHandler extends ObjectChangeHandlerAdapter {
    @Autowired
    private StructureTreeManager structureTreeManager;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IResourceStructureService resourceStructureService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(ResourceStructure.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        ResourceStructure resourceStructure = changeRecord.readMessageBody(ResourceStructure.class);
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        ResourceStructure resourceStructure = changeRecord.readMessageBody(ResourceStructure.class);
        // 关联的设备的resourceStructureId设置为null
        equipmentService.clearEquipmentResourceStructureId(resourceStructure.getResourceStructureId());
        // 如果层级底下有子节点，删除子节点
        List<ResourceStructure> childResourceStructures = resourceStructureService.findByParentResourceStructureId(resourceStructure.getResourceStructureId());
        if (childResourceStructures != null && !childResourceStructures.isEmpty()) {
            for (ResourceStructure childResourceStructure : childResourceStructures) {
                resourceStructureService.deleteByID(childResourceStructure.getResourceStructureId());
            }
        }
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        ResourceStructure resourceStructure = changeRecord.readMessageBody(ResourceStructure.class);
        // 检测子层级的levelpath和orginidparentid是否一致
        if (resourceStructure != null) {
            List<ResourceStructure> resourceStructures = resourceStructureService.findByParentResourceStructureId(resourceStructure.getResourceStructureId());
            if (resourceStructures != null && !resourceStructures.isEmpty()) {
                for (ResourceStructure childResourceStructure : resourceStructures) {
                    if (!childResourceStructure.getLevelOfPath().contains(resourceStructure.getLevelOfPath())) {
                        childResourceStructure.setLevelOfPath(resourceStructure.getLevelOfPath() + "." + childResourceStructure.getResourceStructureId());
                        if (childResourceStructure.getOriginParentId() != null) {
                            childResourceStructure.setOriginParentId(resourceStructure.getOriginId());
                        }
                        resourceStructureService.update(childResourceStructure);
                    }
                }
            }
        }
    }
}

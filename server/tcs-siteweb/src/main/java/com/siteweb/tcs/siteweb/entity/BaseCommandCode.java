package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import java.io.Serializable;

/**
 * 基础命令代码实体类
 */
@Data
@TableName("tbl_basecommandcode")
public class BaseCommandCode implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 代码ID
     */
    @TableId(value = "CodeId", type = IdType.INPUT)
    private Integer codeId;

    /**
     * 命令
     */
    @TableField("Command")
    private String command;

    /**
     * 描述
     */
    @TableField("Description")
    private String description;
}
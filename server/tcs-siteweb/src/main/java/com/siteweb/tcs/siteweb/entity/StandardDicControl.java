package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Standard dictionary control entity
 */
@Data
@TableName("tbl_standarddiccontrol")
public class StandardDicControl implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "StandardDicId")
    private Integer standardDicId;

    @TableField("StandardType")
    private Integer standardType;

    @TableField("EquipmentLogicClassId")
    private Integer equipmentLogicClassId;

    @TableField("EquipmentLogicClass")
    private String equipmentLogicClass;

    @TableField("ControlLogicClassId")
    private Integer controlLogicClassId;

    @TableField("ControlLogicClass")
    private String controlLogicClass;

    @TableField("ControlStandardName")
    private String controlStandardName;

    @TableField("NetManageId")
    private String netManageId;

    @TableField("StationCategory")
    private Integer stationCategory;

    @TableField("ModifyType")
    private Integer modifyType;

    @TableField("Description")
    private String description;

    @TableField("ExtendFiled1")
    private String extendFiled1;

    @TableField("ExtendFiled2")
    private String extendFiled2;
}

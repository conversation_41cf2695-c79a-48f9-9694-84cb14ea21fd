package com.siteweb.tcs.siteweb.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.Door;
import com.siteweb.tcs.siteweb.entity.DoorCard;
import com.siteweb.tcs.siteweb.mapper.DoorCardMapper;
import com.siteweb.tcs.siteweb.service.IDoorCardService;
import com.siteweb.tcs.siteweb.service.IDoorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Door Card Service Implementation
 */
@Slf4j
@Service
public class DoorCardServiceImpl extends ServiceImpl<DoorCardMapper, DoorCard> implements IDoorCardService {

    @Autowired
    private DoorCardMapper doorCardMapper;
    @Autowired
    @Lazy
    private IDoorService doorService;

    @Override
    public void deleteByEquipmentId(Integer equipmentId) {
        List<Door> doorList = doorService.getDoorByEquipmentId(equipmentId);
        if(CollUtil.isNotEmpty(doorList)) {
            doorList.forEach(door -> {
                LambdaQueryWrapper<DoorCard> queryWrapper = Wrappers.lambdaQuery(DoorCard.class)
                        .eq(DoorCard::getDoorId, door.getDoorId());
                doorCardMapper.delete(queryWrapper);
            });
        }
        log.info("删除设备门禁卡成功, equipmentId: {}", equipmentId);
    }

    @Override
    public List<Integer> findCardIdByEquipmentId(Integer equipmentId) {
        List<Door> doorList = doorService.getDoorByEquipmentId(equipmentId);
        if(CollUtil.isEmpty(doorList)) {
            LambdaQueryWrapper<DoorCard> queryWrapper = Wrappers.lambdaQuery(DoorCard.class);
            queryWrapper.in(DoorCard::getDoorId, doorList.stream().map(Door::getDoorId).collect(Collectors.toList()));
            return doorCardMapper.selectList(queryWrapper).stream()
                    .map(DoorCard::getCardId)
                    .collect(Collectors.toList());
        }
        return null;
    }
}

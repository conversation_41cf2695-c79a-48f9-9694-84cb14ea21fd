package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * Card entity
 */
@Data
@TableName("tbl_card")
public class Card implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "CardId")
    private Integer cardId;

    @TableField("CardCode")
    private String cardCode;

    @TableField("CardName")
    private String cardName;

    @TableField("CardCategory")
    private Integer cardCategory;

    @TableField("CardGroup")
    private Integer cardGroup;

    @TableField("UserId")
    private Integer userId;

    @TableField("StationId")
    private Integer stationId;

    @TableField("CardStatus")
    private Integer cardStatus;

    @TableField("StartTime")
    private LocalDateTime startTime;

    @TableField("EndTime")
    private LocalDateTime endTime;

    @TableField("RegisterTime")
    private LocalDateTime registerTime;

    @TableField("UnRegisterTime")
    private LocalDateTime unRegisterTime;

    @TableField("LostTime")
    private LocalDateTime lostTime;

    @TableField("Description")
    private String description;
}

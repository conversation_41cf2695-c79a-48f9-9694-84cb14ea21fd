package com.siteweb.tcs.siteweb.config;

import org.eclipse.paho.client.mqttv3.MqttAsyncClient;
import org.eclipse.paho.client.mqttv3.MqttConnectOptions;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.UUID;

/**
 * MQTT配置类
 */
@Configuration
public class MqttConfig {

    @Value("${mqtt.broker.url:tcp://localhost:1883}")
    private String brokerUrl;

    @Value("${mqtt.client.id:tcs-siteweb-}")
    private String clientId;

    @Value("${mqtt.username:}")
    private String username;

    @Value("${mqtt.password:}")
    private String password;

    @Bean
    public MqttConnectOptions mqttConnectOptions() {
        MqttConnectOptions options = new MqttConnectOptions();
        
        if (username != null && !username.isEmpty()) {
            options.setUserName(username);
        }
        
        if (password != null && !password.isEmpty()) {
            options.setPassword(password.toCharArray());
        }
        
        options.setAutomaticReconnect(true);
        options.setCleanSession(true);
        options.setConnectionTimeout(30);
        options.setKeepAliveInterval(60);
        
        return options;
    }

    @Bean
    public MqttAsyncClient mqttClient(MqttConnectOptions options) throws MqttException {
        String clientIdWithSuffix = clientId + UUID.randomUUID().toString().replace("-", "");
        MqttAsyncClient client = new MqttAsyncClient(brokerUrl, clientIdWithSuffix);
        try {
            client.connect(options);
        } catch (MqttException e) {
            // 连接失败时记录错误并继续，服务会在后续尝试重新连接
            e.printStackTrace();
        }
        return client;
    }
} 
package com.siteweb.tcs.siteweb.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * Logic class entry entity
 */
@Data
@TableName("tbl_logicclassentry")
public class LogicClassEntry implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "EntryId")
    private Integer entryId;

    @TableField("EntryCategory")
    private Integer entryCategory;

    @TableField("LogicClassId")
    private Integer logicClassId;

    @TableField("LogicClass")
    private String logicClass;

    @TableField("StandardType")
    private Integer standardType;

    @TableField("Description")
    private String description;
}

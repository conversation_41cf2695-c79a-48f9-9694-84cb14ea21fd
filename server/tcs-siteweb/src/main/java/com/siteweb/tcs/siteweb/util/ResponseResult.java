package com.siteweb.tcs.siteweb.util;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * API Response Result
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ResponseResult {
    
    /**
     * 成功状态码
     */
    private static final int SUCCESS_CODE = 0;
    
    /**
     * 默认失败状态码
     */
    private static final int DEFAULT_FAIL_CODE = -1;
    
    /**
     * 状态码（0表示成功，非0表示失败）
     */
    private int code;
    
    /**
     * 消息
     */
    private String message;
    
    /**
     * 数据
     */
    private Object data;
    
    /**
     * 创建成功响应
     * @return 响应结果
     */
    public static ResponseResult success() {
        return new ResponseResult(SUCCESS_CODE, "success", null);
    }
    
    /**
     * 创建成功响应
     * @param data 响应数据
     * @return 响应结果
     */
    public static ResponseResult success(Object data) {
        return new ResponseResult(SUCCESS_CODE, "success", data);
    }
    
    /**
     * 创建失败响应
     * @param message 错误消息
     * @return 响应结果
     */
    public static ResponseResult fail(String message) {
        return new ResponseResult(DEFAULT_FAIL_CODE, message, null);
    }
    
    /**
     * 创建失败响应
     * @param code 错误代码
     * @param message 错误消息
     * @return 响应结果
     */
    public static ResponseResult fail(int code, String message) {
        return new ResponseResult(code, message, null);
    }
} 
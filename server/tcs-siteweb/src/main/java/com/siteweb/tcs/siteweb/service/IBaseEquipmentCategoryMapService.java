package com.siteweb.tcs.siteweb.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.siteweb.tcs.siteweb.entity.BaseEquipmentCategoryMap;

/**
 * Base Equipment Category Map Service Interface
 */
public interface IBaseEquipmentCategoryMapService extends IService<BaseEquipmentCategoryMap> {
    /**
     * 删除所有设备分类映射
     * @return 删除的记录数
     */
    int deleteAll();

    /**
     * 批量保存移动标准的设备分类映射
     * @return 是否保存成功
     */
    boolean batchsaveYiDongEquipmentCategoryMap();

    /**
     * 批量保存电信标准的设备分类映射
     * @return 是否保存成功
     */
    boolean batchsaveDianXinEquipmentCategoryMap();
}

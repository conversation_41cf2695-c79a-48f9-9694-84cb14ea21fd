package com.siteweb.tcs.siteweb.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.siteweb.tcs.siteweb.entity.SignalBaseDic;
import com.siteweb.tcs.siteweb.mapper.SignalBaseDicMapper;
import com.siteweb.tcs.siteweb.service.ISignalBaseDicService;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.function.IntFunction;

/**
 * 基础信号字典 服务实现类
 */
@Service
public class SignalBaseDicServiceImpl extends ServiceImpl<SignalBaseDicMapper, SignalBaseDic> implements ISignalBaseDicService {

    @Override
    public SignalBaseDic findByBaseTypeId(Long baseTypeId) {
        return getById(baseTypeId);
    }

    @Override
    public void updateBaseClassStandardDictionary(Integer equipmentTemplateId) {
        // 由BaseDicServiceImpl统一处理
    }

    @Override
    public boolean existsByBaseTypeId(Long baseTypeId) {
        return count(Wrappers.lambdaQuery(SignalBaseDic.class)
                .eq(SignalBaseDic::getBaseTypeId, baseTypeId)) > 0;
    }

    @Override
    public void generateBaseDic(Long baseTypeId, Long sourceId) {
        baseMapper.generateSignalBaseDic(baseTypeId, sourceId);
        
        // 更新baseTypeName
        SignalBaseDic signalBaseDic = findByBaseTypeId(baseTypeId);
        if (signalBaseDic != null && signalBaseDic.getBaseNameExt() != null && !signalBaseDic.getBaseNameExt().isEmpty()) {
            Long moduleNo = baseTypeId % 1000;
            String baseTypeName = signalBaseDic.getBaseNameExt().replace("{0}", moduleNo.toString());
            signalBaseDic.setBaseTypeName(baseTypeName);
            updateById(signalBaseDic);
        }
    }

    @Override
    public void processBaseTypeIdList(IntFunction<List<Long>> findBaseTypeIdsFunction, com.siteweb.tcs.siteweb.service.IBaseDicService baseDicService, Integer equipmentTemplateId) {
        // 由BaseDicServiceImpl统一处理
    }
}
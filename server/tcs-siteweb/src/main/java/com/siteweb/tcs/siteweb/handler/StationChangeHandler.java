package com.siteweb.tcs.siteweb.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.siteweb.tcs.siteweb.change.ChangeRecord;
import com.siteweb.tcs.siteweb.change.ObjectChangeHandlerAdapter;
import com.siteweb.tcs.siteweb.entity.*;
import com.siteweb.tcs.siteweb.enums.ChangeOperatorEnum;
import com.siteweb.tcs.siteweb.enums.TableIdentityEnum;
import com.siteweb.tcs.siteweb.service.IConfigChangeMacroLogService;
import com.siteweb.tcs.siteweb.service.IEquipmentService;
import com.siteweb.tcs.siteweb.service.IHouseService;
import com.siteweb.tcs.siteweb.service.IMonitorUnitService;
import com.siteweb.tcs.siteweb.service.IPrimaryKeyValueService;
import com.siteweb.tcs.siteweb.service.IResourceStructureService;
import com.siteweb.tcs.siteweb.service.ISamplerUnitService;
import com.siteweb.tcs.siteweb.service.IStationProjectInfoService;
import com.siteweb.tcs.siteweb.service.IStationService;
import com.siteweb.tcs.siteweb.service.IStationStructureMapService;
import com.siteweb.tcs.siteweb.service.IStationStructureService;
import com.siteweb.tcs.siteweb.service.ISwatchStationService;
import com.siteweb.tcs.siteweb.service.ITslMonitorUnitEventService;
import com.siteweb.tcs.siteweb.service.ITslMonitorUnitSignalService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 站点数据变更处理器
 *
 * <AUTHOR> (2024-03-14)
 **/
@Slf4j
@Component
public class StationChangeHandler extends ObjectChangeHandlerAdapter {

    @Autowired
    private IStationProjectInfoService stationProjectInfoService;

    @Autowired
    private IHouseService houseService;

    @Autowired
    private ITslMonitorUnitSignalService tslMonitorUnitSignalService;

    @Autowired
    private ITslMonitorUnitEventService tslMonitorUnitEventService;

    @Autowired
    private ISamplerUnitService samplerUnitService;

    @Autowired
    private IMonitorUnitService monitorUnitService;

    @Autowired
    private IEquipmentService equipmentService;

    @Autowired
    private IStationStructureMapService stationStructureMapService;

    @Autowired
    private ISwatchStationService swatchStationService;

    @Autowired
    private IResourceStructureService resourceStructureService;

    @Autowired
    private IStationStructureService stationStructureService;

    @Autowired
    private IConfigChangeMacroLogService configChangeMacroLogService;

    @Autowired
    private IPrimaryKeyValueService primaryKeyValueService;

    @Override
    protected List<Class<?>> doRegisterHandler() {
        return List.of(Station.class);
    }

    @Override
    public void onCreate(ChangeRecord changeRecord) {
        Station station = changeRecord.readMessageBody(Station.class);
        log.info("站点创建成功,stationId:{},stationName:{}", station.getStationId(), station.getStationName());

        // 如果是普通站点（bordNumber为0），则创建对应的ResourceStructure
        if (station.getBordNumber() != null && station.getBordNumber() == 0) {
            // 查询站点关联的StationStructureMap
            StationStructureMap stationStructureMap = stationStructureMapService.findStationStructureMapByStationId(station.getStationId());
            if (stationStructureMap != null) {
                // 查询StationStructure
                StationStructure stationStructure = stationStructureService.findStructureById(stationStructureMap.getStructureId());
                if (stationStructure != null) {
                    // 查询父级ResourceStructure
                    ResourceStructure parentResourceStructure = resourceStructureService.findByOriginIdAndStructureType(stationStructure.getStructureId(), 103);
                    if (parentResourceStructure != null) {
                        // 创建站点的资源结构
                        ResourceStructure resourceStructure = new ResourceStructure();
                        resourceStructure.setResourceStructureName(station.getStationName());
                        resourceStructure.setParentResourceStructureId(parentResourceStructure.getResourceStructureId());
                        resourceStructure.setStructureTypeId(102); // 站点类型
                        resourceStructure.setOriginId(station.getStationId());
                        resourceStructure.setSortValue(0);
//                        resourceStructure.setCreateTime(LocalDateTime.now());
//                        resourceStructure.setUpdateTime(LocalDateTime.now());
                        
                        boolean result = resourceStructureService.save(resourceStructure);
                        if (result) {
                            log.info("创建站点资源结构成功：stationId={}, resourceStructureId={}", 
                                    station.getStationId(), resourceStructure.getResourceStructureId());
                            
                            // 通知所有人更新层级树
                            configChangeMacroLogService.configChangeLog(
                                    station.getStationId().toString(),
                                    TableIdentityEnum.RESOURCE_STRUCTURE.ordinal(),
                                    ChangeOperatorEnum.CREATE);
                        } else {
                            log.error("创建站点资源结构失败：stationId={}", station.getStationId());
                        }
                    } else {
                        createResourceStructure(station);
                    }
                } else {
                    createResourceStructure(station);
                }
            } else {
                createResourceStructure(station);
            }
        }
    }

    @Override
    public void onDelete(ChangeRecord changeRecord) {
        Station station = changeRecord.readMessageBody(Station.class);
        log.info("站点删除成功,stationId:{},stationName:{}", station.getStationId(), station.getStationName());

        // 删除站点项目信息
        stationProjectInfoService.remove(new LambdaQueryWrapper<StationProjectInfo>().eq(StationProjectInfo::getStationId, station.getStationId()));

        // 删除房间信息
        houseService.remove(new LambdaQueryWrapper<House>().eq(h -> h.getStationId(), station.getStationId()));

        // 删除监控单元信号
        tslMonitorUnitSignalService.deleteByStationId(station.getStationId());

        // 删除监控单元事件
        tslMonitorUnitEventService.deleteByStationId(station.getStationId());

        // 删除监控单元
        monitorUnitService.remove(new LambdaQueryWrapper<MonitorUnit>().eq(m -> m.getStationId(), station.getStationId()));

        // 删除设备
        equipmentService.deleteByStationId(station.getStationId());

        // 删除站点结构映射
        stationStructureMapService.deleteByStationId(station.getStationId());

        // 删除样板站
        swatchStationService.deleteByStationId(station.getStationId());

        // 删除层级结构
        ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(station.getStationId(), 102);
        if (resourceStructure != null) {
            resourceStructureService.deleteByID(resourceStructure.getResourceStructureId());
            
            // 通知所有人更新层级树
            configChangeMacroLogService.configChangeLog(
                    station.getStationId().toString(),
                    TableIdentityEnum.RESOURCE_STRUCTURE.ordinal(),
                    ChangeOperatorEnum.DELETE);
        }
    }

    @Override
    public void onUpdate(ChangeRecord changeRecord) {
        Station station = changeRecord.readMessageBody(Station.class);
        log.trace("站点修改成功,stationId:{},stationName:{}", station.getStationId(), station.getStationName());

        // 如果是普通站点（bordNumber为0），则更新对应的ResourceStructure
        if (station.getBordNumber() != null && station.getBordNumber() == 0) {
            ResourceStructure resourceStructure = resourceStructureService.findByOriginIdAndStructureType(station.getStationId(), 102);
            if (resourceStructure != null && !resourceStructure.getResourceStructureName().equals(station.getStationName())) {
                resourceStructure.setResourceStructureName(station.getStationName());
                resourceStructureService.update(resourceStructure);
                
                // 通知所有人更新层级树
                configChangeMacroLogService.configChangeLog(
                        station.getStationId().toString(),
                        TableIdentityEnum.RESOURCE_STRUCTURE.ordinal(),
                        ChangeOperatorEnum.UPDATE);
            } else if (resourceStructure == null) {
                // 如果不存在，则创建
                createResourceStructure(station);
            }
        }
    }

    /**
     * 创建资源结构
     *
     * @param station 站点
     */
    private void createResourceStructure(Station station) {
        try {
            // 获取根节点
            Integer rootId = resourceStructureService.getTreeRootId();
            if (rootId == null) {
                log.error("创建站点资源结构失败：根节点不存在");
                return;
            }

            // 创建资源结构
            ResourceStructure resourceStructure = new ResourceStructure();
            resourceStructure.setResourceStructureName(station.getStationName());
            resourceStructure.setParentResourceStructureId(rootId);
            resourceStructure.setStructureTypeId(102); // 站点类型
            resourceStructure.setOriginId(station.getStationId());
            resourceStructure.setSortValue(0);
//            resourceStructure.setCreateTime(LocalDateTime.now());
//            resourceStructure.setUpdateTime(LocalDateTime.now());

            boolean result = resourceStructureService.save(resourceStructure);
            if (result) {
                log.info("创建站点资源结构成功：stationId={}, resourceStructureId={}", 
                        station.getStationId(), resourceStructure.getResourceStructureId());
                
                // 通知所有人更新层级树
                configChangeMacroLogService.configChangeLog(
                        station.getStationId().toString(),
                        TableIdentityEnum.RESOURCE_STRUCTURE.ordinal(),
                        ChangeOperatorEnum.CREATE);
            } else {
                log.error("创建站点资源结构失败：stationId={}", station.getStationId());
            }
        } catch (Exception e) {
            log.error("创建站点资源结构异常：", e);
        }
    }
}

package com.siteweb.tcs.siteweb.cache;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.siteweb.tcs.siteweb.entity.ResourceStructure;
import com.siteweb.tcs.siteweb.mapper.ResourceStructureMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Component
public class ResourceStructureCache {

    private final Map<Integer, ResourceStructure> cache = new ConcurrentHashMap<>();
    private List<ResourceStructure> rootCache = List.of(); // Cache for root nodes

    @Autowired
    private ResourceStructureMapper structureMapper;

    @PostConstruct
    public void init() {
        reload();
    }

    public void reload() {
        cache.clear();
        List<ResourceStructure> list = structureMapper.selectList(Wrappers.emptyWrapper());
        if (list != null) {
            for (ResourceStructure item : list) {
                cache.put(item.getResourceStructureId(), item);
            }
            // Also reload root cache
            rootCache = list.stream()
                            .filter(rs -> rs.getParentResourceStructureId() == null || rs.getParentResourceStructureId() == 0) // Adjust root condition if necessary
                            .collect(Collectors.toList());
        }
         // Log cache reload completion
    }

    public ResourceStructure get(Integer key) {
        return cache.get(key);
    }

    public void put(Integer key, ResourceStructure value) {
        cache.put(key, value);
        // Consider if roots need updating on individual put
        if (value.getParentResourceStructureId() == null || value.getParentResourceStructureId() == 0) {
            // Simple full reload of roots for now, can be optimized
            reloadRoots(); 
        }
    }

    public void remove(Integer key) {
        cache.remove(key);
        // Consider if roots need updating on individual remove
        rootCache.removeIf(rs -> rs.getResourceStructureId().equals(key));
    }

    public Collection<ResourceStructure> values() {
        return cache.values();
    }

    public List<ResourceStructure> getRoots() {
        if (rootCache.isEmpty() && !cache.isEmpty()) { // If roots not loaded but main cache is, try to load roots
             reloadRoots();
        }
        return List.copyOf(rootCache); // Return a copy for immutability
    }
    
    private void reloadRoots() {
        rootCache = cache.values().stream()
                        .filter(rs -> rs.getParentResourceStructureId() == null || rs.getParentResourceStructureId() == 0) // Adjust root condition
                        .collect(Collectors.toList());
    }
} 
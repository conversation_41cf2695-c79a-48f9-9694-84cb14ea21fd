<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EventConditionMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.EventCondition">
        <id column="Id" property="id" />
        <result column="EventConditionId" property="eventConditionId" />
        <result column="EquipmentTemplateId" property="equipmentTemplateId" />
        <result column="EventId" property="eventId" />
        <result column="StartOperation" property="startOperation" />
        <result column="StartCompareValue" property="startCompareValue" />
        <result column="StartDelay" property="startDelay" />
        <result column="EndOperation" property="endOperation" />
        <result column="EndCompareValue" property="endCompareValue" />
        <result column="EndDelay" property="endDelay" />
        <result column="Frequency" property="frequency" />
        <result column="FrequencyThreshold" property="frequencyThreshold" />
        <result column="Meanings" property="meanings" />
        <result column="EquipmentState" property="equipmentState" />
        <result column="BaseTypeId" property="baseTypeId" />
        <result column="EventSeverity" property="eventSeverity" />
        <result column="StandardName" property="standardName" />
    </resultMap>
    
    <!-- 基础列 -->
    <sql id="Base_Column_List">
        Id, EventConditionId, EquipmentTemplateId, EventId, StartOperation, StartCompareValue, StartDelay, 
        EndOperation, EndCompareValue, EndDelay, Frequency, FrequencyThreshold, Meanings, EquipmentState, 
        BaseTypeId, EventSeverity, StandardName
    </sql>
    
    <!-- 根据设备模板ID和事件ID批量删除事件条件 -->
    <delete id="deleteByEvent">
        DELETE FROM 
            tbl_eventcondition 
        WHERE 
            EquipmentTemplateId = #{equipmentTemplateId} AND EventId = #{eventId}
    </delete>
    
    <!-- 批量更新事件条件 -->
    <update id="batchUpdate">
        <foreach collection="conditions" item="condition" separator=";">
            UPDATE tbl_eventcondition
            <set>
                <if test="condition.startOperation != null">StartOperation = #{condition.startOperation},</if>
                <if test="condition.startCompareValue != null">StartCompareValue = #{condition.startCompareValue},</if>
                <if test="condition.startDelay != null">StartDelay = #{condition.startDelay},</if>
                <if test="condition.endOperation != null">EndOperation = #{condition.endOperation},</if>
                <if test="condition.endCompareValue != null">EndCompareValue = #{condition.endCompareValue},</if>
                <if test="condition.endDelay != null">EndDelay = #{condition.endDelay},</if>
                <if test="condition.frequency != null">Frequency = #{condition.frequency},</if>
                <if test="condition.frequencyThreshold != null">FrequencyThreshold = #{condition.frequencyThreshold},</if>
                <if test="condition.meanings != null">Meanings = #{condition.meanings},</if>
                <if test="condition.equipmentState != null">EquipmentState = #{condition.equipmentState},</if>
                <if test="condition.baseTypeId != null">BaseTypeId = #{condition.baseTypeId},</if>
                <if test="condition.eventSeverity != null">EventSeverity = #{condition.eventSeverity},</if>
                <if test="condition.standardName != null">StandardName = #{condition.standardName}</if>
            </set>
            WHERE EquipmentTemplateId = #{condition.equipmentTemplateId} AND EventId = #{condition.eventId} AND EventConditionId = #{condition.eventConditionId}
        </foreach>
    </update>
    
    <!-- 根据设备模板ID和事件ID查询事件条件 -->
    <select id="findByEventId" resultMap="BaseResultMap">
        SELECT 
            <include refid="Base_Column_List" />
        FROM 
            tbl_eventcondition 
        WHERE 
            EquipmentTemplateId = #{equipmentTemplateId} AND EventId = #{eventId}
        ORDER BY 
            EventConditionId
    </select>

    <select id="findMaxEventByEquipmentTemplateId" resultType="com.siteweb.tcs.siteweb.entity.EventCondition">
        SELECT *
        FROM TBL_EventCondition WHERE EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY EventId DESC
        LIMIT 1
    </select>

    <select id="findMaxEventConditionByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT MAX(EventConditionId)
        FROM TBL_EventCondition
        WHERE EventId = #{eventId}
        AND EquipmentTemplateId = #{equipmentTemplateId}
    </select>
</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.EquipmentTemplateMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.EquipmentTemplate">
        <id column="EquipmentTemplateId" property="equipmentTemplateId"/>
        <result column="EquipmentTemplateName" property="equipmentTemplateName"/>
        <result column="ParentTemplateId" property="parentTemplateId"/>
        <result column="Memo" property="memo"/>
        <result column="ProtocolCode" property="protocolCode"/>
        <result column="EquipmentCategory" property="equipmentCategory"/>
        <result column="EquipmentType" property="equipmentType"/>
        <result column="Property" property="property"/>
        <result column="Description" property="description"/>
        <result column="EquipmentStyle" property="equipmentStyle"/>
        <result column="Unit" property="unit"/>
        <result column="Vendor" property="vendor"/>
        <result column="Photo" property="photo"/>
        <result column="EquipmentBaseType" property="equipmentBaseType"/>
        <result column="StationCategory" property="stationCategory"/>
        <result column="ExtendField1" property="extendField1"/>
    </resultMap>
    <!-- 通用查询结果?-->
    <sql id="Base_Column_List">
        <!-- TODO: 请根�?{entityName}.java实体字段补充列名 -->
    </sql>

    <select id="findByNameLike" resultMap="BaseResultMap">
        SELECT *
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateName LIKE CONCAT('%', #{equipmentTemplateName}, '%')
    </select>

    <select id="getBInterfaceDeviceTemplateRootId" resultType="java.lang.Integer">
        SELECT EquipmentTemplateId
        FROM tbl_equipmenttemplate
        WHERE ProtocolCode = 'BInterfaceProtocol' AND ParentTemplateId IS NULL
        LIMIT 1
    </select>

    <select id="countByParentTemplateId" resultType="int">
        SELECT COUNT(*)
        FROM tbl_equipmenttemplate
        WHERE ParentTemplateId = #{parentTemplateId}
    </select>

    <select id="queryTemplateByVO" resultMap="BaseResultMap" parameterType="com.siteweb.tcs.siteweb.vo.EquipmentTemplateVO">
        SELECT *
        FROM tbl_equipmenttemplate
        <where>
            <if test="vo.equipmentTemplateId != null">
                AND EquipmentTemplateId = #{vo.equipmentTemplateId}
            </if>
            <if test="vo.equipmentTemplateName != null and vo.equipmentTemplateName != ''">
                AND EquipmentTemplateName LIKE CONCAT('%', #{vo.equipmentTemplateName}, '%')
            </if>
            <if test="vo.parentTemplateId != null">
                AND ParentTemplateId = #{vo.parentTemplateId}
            </if>
            <if test="vo.protocolCode != null and vo.protocolCode != ''">
                AND ProtocolCode = #{vo.protocolCode}
            </if>
            <if test="vo.equipmentCategory != null">
                AND EquipmentCategory = #{vo.equipmentCategory}
            </if>
            <if test="vo.equipmentType != null">
                AND EquipmentType = #{vo.equipmentType}
            </if>
            <if test="vo.equipmentBaseType != null">
                AND EquipmentBaseType = #{vo.equipmentBaseType}
            </if>
            <if test="vo.stationCategory != null">
                AND StationCategory = #{vo.stationCategory}
            </if>
            <if test="vo.extendField1 != null and vo.extendField1 != ''">
                AND ExtendField1 = #{vo.extendField1}
            </if>
             <!-- Add other fields from EquipmentTemplateVO for query as needed -->
        </where>
        ORDER BY EquipmentTemplateId DESC
    </select>

    <update id="updateEquipmentBaseTypeToNull">
        UPDATE tbl_equipmenttemplate
        SET BaseTypeId = NULL
    </update>

    <select id="findEquipmentTemplateIdByEquipmentTemplateIdDiv" resultType="java.lang.Integer">
        SELECT EquipmentTemplateId
        FROM tbl_equipmenttemplate
        WHERE EquipmentTemplateIdDiv = #{equipmentTemplateIdDiv}
    </select>

</mapper>

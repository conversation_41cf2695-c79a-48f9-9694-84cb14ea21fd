<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.siteweb.tcs.siteweb.mapper.ControlMapper">
    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.siteweb.tcs.siteweb.entity.Control">
        <!-- TODO: 请根据Control.java实体字段补充映射关系 -->
    </resultMap>
    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        <!-- TODO: 请根据Control.java实体字段补充列名 -->
    </sql>

    <resultMap id="ControlConfigItemResultMap" type="com.siteweb.tcs.siteweb.dto.ControlConfigItem">
        <id property="id" column="Id"/>
        <result property="equipmentTemplateId" column="EquipmentTemplateId"/>
        <result property="controlId" column="ControlId"/>
        <result property="controlName" column="ControlName"/>
        <result property="controlCategory" column="ControlCategory"/>
        <result property="cmdToken" column="CmdToken"/>
        <result property="baseTypeId" column="BaseTypeId"/>
        <result property="controlSeverity" column="ControlSeverity"/>
        <result property="signalId" column="SignalId"/>
        <result property="timeOut" column="TimeOut"/>
        <result property="retry" column="Retry"/>
        <result property="description" column="Description"/>
        <result property="enable" column="Enable"/>
        <result property="visible" column="Visible"/>
        <result property="displayIndex" column="DisplayIndex"/>
        <result property="commandType" column="CommandType"/>
        <result property="controlType" column="ControlType"/>
        <result property="dataType" column="DataType"/>
        <result property="maxValue" column="MaxValue"/>
        <result property="minValue" column="MinValue"/>
        <result property="defaultValue" column="DefaultValue"/>
        <result property="moduleNo" column="ModuleNo"/>
        <collection property="controlMeaningsList" ofType="com.siteweb.tcs.siteweb.entity.ControlMeanings">
            <id property="id" column="CM_Id"/>
            <result property="equipmentTemplateId" column="CM_EquipmentTemplateId"/>
            <result property="controlId" column="CM_ControlId"/>
            <result property="parameterValue" column="ParameterValue"/>
            <result property="meanings" column="Meanings"/>
            <result property="baseCondId" column="BaseCondId"/>
        </collection>
    </resultMap>

    <select id="findMaxControlIdByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(ControlId), 0) FROM tbl_control WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <select id="findMaxDisplayIndexByEquipmentTemplateId" resultType="java.lang.Integer">
        SELECT COALESCE(MAX(DisplayIndex), 0) FROM tbl_control WHERE EquipmentTemplateId = #{equipmentTemplateId}
    </select>

    <delete id="deleteControl">
        DELETE FROM tbl_control WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
    </delete>
    
    <select id="findControlItemByEquipmentTemplateId" resultMap="ControlConfigItemResultMap">
        SELECT 
            c.*,
            cm.Id AS CM_Id, 
            cm.EquipmentTemplateId AS CM_EquipmentTemplateId, 
            cm.ControlId AS CM_ControlId,
            cm.ParameterValue, 
            cm.Meanings, 
            cm.BaseCondId
        FROM tbl_control c
        LEFT JOIN tbl_controlmeanings cm ON c.EquipmentTemplateId = cm.EquipmentTemplateId AND c.ControlId = cm.ControlId
        WHERE c.EquipmentTemplateId = #{equipmentTemplateId}
        ORDER BY c.DisplayIndex, cm.ParameterValue
    </select>

    <select id="findByEquipmentTemplateIdAndControlId" resultMap="ControlConfigItemResultMap">
        SELECT 
            c.*,
            cm.Id AS CM_Id, 
            cm.EquipmentTemplateId AS CM_EquipmentTemplateId, 
            cm.ControlId AS CM_ControlId,
            cm.ParameterValue, 
            cm.Meanings, 
            cm.BaseCondId
        FROM tbl_control c
        LEFT JOIN tbl_controlmeanings cm ON c.EquipmentTemplateId = cm.EquipmentTemplateId AND c.ControlId = cm.ControlId
        WHERE c.EquipmentTemplateId = #{equipmentTemplateId} AND c.ControlId = #{controlId}
        ORDER BY cm.ParameterValue
    </select>
    
    <insert id="insertControl" parameterType="com.siteweb.tcs.siteweb.entity.Control" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO tbl_control (
            EquipmentTemplateId, ControlId, ControlName, ControlCategory, CmdToken, BaseTypeId, 
            ControlSeverity, SignalId, TimeOut, Retry, Description, Enable, Visible, DisplayIndex, 
            CommandType, ControlType, DataType, MaxValue, MinValue, DefaultValue, ModuleNo
        ) VALUES (
            #{equipmentTemplateId}, #{controlId}, #{controlName}, #{controlCategory}, #{cmdToken}, #{baseTypeId}, 
            #{controlSeverity}, #{signalId}, #{timeOut}, #{retry}, #{description}, #{enable}, #{visible}, #{displayIndex}, 
            #{commandType}, #{controlType}, #{dataType}, #{maxValue}, #{minValue}, #{defaultValue}, #{moduleNo}
        )
    </insert>

    <update id="updateControl" parameterType="com.siteweb.tcs.siteweb.entity.Control">
        UPDATE tbl_control
        <set>
            <if test="controlName != null">ControlName = #{controlName},</if>
            <if test="controlCategory != null">ControlCategory = #{controlCategory},</if>
            <if test="cmdToken != null">CmdToken = #{cmdToken},</if>
            <if test="baseTypeId != null">BaseTypeId = #{baseTypeId},</if>
            <if test="controlSeverity != null">ControlSeverity = #{controlSeverity},</if>
            <if test="signalId != null">SignalId = #{signalId},</if>
            <if test="timeOut != null">TimeOut = #{timeOut},</if>
            <if test="retry != null">Retry = #{retry},</if>
            <if test="description != null">Description = #{description},</if>
            <if test="enable != null">Enable = #{enable},</if>
            <if test="visible != null">Visible = #{visible},</if>
            <if test="displayIndex != null">DisplayIndex = #{displayIndex},</if>
            <if test="commandType != null">CommandType = #{commandType},</if>
            <if test="controlType != null">ControlType = #{controlType},</if>
            <if test="dataType != null">DataType = #{dataType},</if>
            <if test="maxValue != null">MaxValue = #{maxValue},</if>
            <if test="minValue != null">MinValue = #{minValue},</if>
            <if test="defaultValue != null">DefaultValue = #{defaultValue},</if>
            <if test="moduleNo != null">ModuleNo = #{moduleNo}</if>
        </set>
        WHERE EquipmentTemplateId = #{equipmentTemplateId} AND ControlId = #{controlId}
    </update>

    <!-- 查找设备模板中不在控制基类字典中的基类ID -->
    <select id="findBaseTypeIdsNotInControlBaseDicForEquipmentTemplate" resultType="java.lang.Long">
        SELECT DISTINCT c.BaseTypeId
        FROM TBL_Control c
        WHERE c.EquipmentTemplateId = #{equipmentTemplateId}
        AND c.BaseTypeId IS NOT NULL
        AND c.BaseTypeId != 0
        AND c.BaseTypeId NOT IN (SELECT BaseTypeId FROM TBL_ControlBaseDic)
    </select>

</mapper>

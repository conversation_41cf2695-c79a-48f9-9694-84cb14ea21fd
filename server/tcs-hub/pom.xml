<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.siteweb</groupId>
        <artifactId>thing-connect-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>tcs-hub</artifactId>

    <properties>
        <maven.compiler.source>17</maven.compiler.source>
        <maven.compiler.target>17</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>
    <dependencies>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-common</artifactId>
            <version>0.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <!-- Test Dependencies -->
        <dependency>
            <groupId>org.apache.pekko</groupId>
            <artifactId>pekko-testkit_2.13</artifactId>
            <version>${pekko.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <scope>test</scope>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.siteweb.config</groupId>-->
<!--            <artifactId>client</artifactId>-->
<!--            <version>1.0.0</version>-->
<!--            <scope>compile</scope>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <groupId>org.mybatis</groupId>-->
<!--                    <artifactId>mybatis</artifactId>-->
<!--                </exclusion>-->
<!--                <exclusion>-->
<!--                    <groupId>com.baomidou</groupId>-->
<!--                    <artifactId>mybatis-plus</artifactId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
        <dependency>
            <groupId>com.github.ben-manes.caffeine</groupId>
            <artifactId>caffeine</artifactId>
            <version>${caffeine.version}</version>
        </dependency>
        <dependency>
            <groupId>org.influxdb</groupId>
            <artifactId>influxdb-java</artifactId>
            <version>2.24</version>
        </dependency>

        <!-- Hutool dependencies -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool-core.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-json</artifactId>
            <version>${hutool-core.version}</version>
        </dependency>

        <!-- PowerMock dependencies -->
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-module-junit4</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.powermock</groupId>
            <artifactId>powermock-api-mockito2</artifactId>
            <version>2.0.9</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.siteweb</groupId>
            <artifactId>tcs-siteweb</artifactId>
            <version>0.0.1-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>3.5.0</version>
                <executions>
                    <execution>
                        <id>analyze</id>
                        <goals>
                            <goal>analyze</goal>
                        </goals>
                        <configuration>
                            <failOnWarning>false</failOnWarning>
                            <outputXML>true</outputXML>
                            <ignoredDependencies>
                                <ignoredDependency>org.projectlombok:lombok</ignoredDependency>
                            </ignoredDependencies>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>Build-Window</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>install-local-jar</id>
                                <phase>validate</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>cmd.exe</executable>
                                    <arguments>
                                        <argument>/c</argument>
                                        <argument>mvn</argument>
                                        <argument>install:install-file</argument>
                                        <argument>-Dfile=${project.basedir}/../libs/siteweb-config-client-1.0.0-lib.jar</argument>
                                        <argument>-DgroupId=org.siteweb.config</argument>
                                        <argument>-DartifactId=client</argument>
                                        <argument>-Dversion=1.0.0</argument>
                                        <argument>-Dpackaging=jar</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>Build-Linux</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>3.0.0</version>
                        <executions>
                            <execution>
                                <id>install-local-jar</id>
                                <phase>validate</phase>
                                <configuration>
                                    <executable>/bin/sh</executable>
                                    <arguments>
                                        <argument>/c</argument>
                                        <argument>mvn</argument>
                                        <argument>install:install-file</argument>
                                        <argument>-Dfile=${project.basedir}/../libs/siteweb-config-client-1.0.0-lib.jar</argument>
                                        <argument>-DgroupId=org.siteweb.config</argument>
                                        <argument>-DartifactId=client</argument>
                                        <argument>-Dversion=1.0.0</argument>
                                        <argument>-Dpackaging=jar</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

</project>
package com.siteweb.tcs.hub.mock;

import lombok.Data;

/**
 * 信号模拟数据类
 * 用于测试环境的信号数据模拟
 */
@Data
public class Signal {
    private String id;
    private String name;
    private String monitorUnitId;
    private String type;
    private String value;
    private String unit;
    private String timestamp;
    
    // 测试数据构造方法
    public static Signal createMockSignal() {
        Signal signal = new Signal();
        signal.setId("MOCK-SIG-001");
        signal.setName("Mock Signal");
        signal.setMonitorUnitId("MOCK-MU-001");
        signal.setType("Analog");
        signal.setValue("25.5");
        signal.setUnit("°C");
        signal.setTimestamp(String.valueOf(System.currentTimeMillis()));
        return signal;
    }
}
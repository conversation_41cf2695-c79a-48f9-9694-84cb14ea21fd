package com.siteweb.tcs.hub.dal.dto;

import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.entity.ForeignControl;
import com.siteweb.tcs.hub.domain.letter.enums.LifeCycleEventType;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class ControlDTO {
    private Integer id;
    private Integer equipmentTemplateId;
    private Integer controlId;
    private String controlName;
    private Integer controlCategory;
    private String cmdToken;
    private Double baseTypeId;
    private Integer controlSeverity;
    private Integer signalId;
    private Double timeOut;
    private Integer retry;
    private String description;
    private Boolean enable;
    private Boolean visible;
    private Integer displayIndex;
    private Integer commandType;
    private Short controlType;
    private Short dataType;
    private Double maxValue;
    private Double minValue;
    private Double defaultValue;
    private Integer moduleNo;

    private ControlMeaningsDTO controlMeaningsDTO;

    private LifeCycleEventType eventType;

    public ForeignControl toForeignControl(String foreignGatewayId,Integer monitorUnitId, String foreignDeviceId,Integer equipmentId,String foreignControlId) {
        ForeignControl foreignControl = new ForeignControl();
        foreignControl.setForeignControlId(foreignControlId)
                .setForeignDeviceId(foreignDeviceId)
                .setMonitorUnitId(monitorUnitId)
                .setForeignGatewayId(foreignGatewayId)
                .setEquipmentId(equipmentId)
                .setControlId(controlId);
        if(ObjectUtil.isNotEmpty(controlMeaningsDTO)) foreignControl.setControlMeaningsId(controlMeaningsDTO.getId());
        return foreignControl;
    }
}

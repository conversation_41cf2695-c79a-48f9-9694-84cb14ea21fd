package com.siteweb.tcs.hub.domain.process;

import lombok.Getter;

@Getter
public enum EnumDeviceConnectState {

    OFFLINE(0, "离线"),
    ONLINE(1, "在线"),
    DISABLE(2,"禁用");

    private final int code;
    private final String description;

    EnumDeviceConnectState(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 用于根据code查找对应的枚举值
    public static EnumDeviceConnectState fromCode(int code) {
        for (EnumDeviceConnectState state : values()) {
            if (state.getCode() == code) {
                return state;
            }
        }
        throw new IllegalArgumentException("Invalid code for DeviceConnectState: " + code);
    }

}

package com.siteweb.tcs.hub.dal.entity.door;

import lombok.Data;

/**
 * @ClassName: DoorInfo
 * @descriptions: 门信息实体
 * @author: xsx
 * @date: 2024/9/19 16:45
 **/
@Data
public class DoorInfo {
    /**
     * 门id
     */
    private Integer doorId;
    /**
     * 门号
     */
    private Integer doorNo;
    /**
     * 门名称
     */
    private String doorName;
    /**
     * 局站id
     */
    private Integer stationId;
    /**
     * 门种类
     */
    private Integer category;
    /**
     * 门密码
     */
    private String password;
    /**
     * 门控制器类型id（用于区分厂家）
     */
    private Integer doorControlId;
}

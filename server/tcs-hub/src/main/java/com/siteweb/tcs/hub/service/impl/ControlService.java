package com.siteweb.tcs.hub.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.siteweb.tcs.hub.dal.dto.ControlDTO;
import com.siteweb.tcs.hub.dal.entity.ForeignControl;
import com.siteweb.tcs.hub.dal.entity.door.DoorTemplateControl;
import com.siteweb.tcs.hub.dal.provider.DoorTemplateProvider;
import com.siteweb.tcs.hub.domain.letter.ForeignControlConfigChange;
import com.siteweb.tcs.siteweb.dto.ControlConfigItem;
import com.siteweb.tcs.siteweb.provider.CommandProvider;
import org.checkerframework.checker.units.qual.C;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class ControlService {

    @Autowired
    private CommandProvider commandProvider;

    @Autowired
    private DoorTemplateProvider doorTemplateProvider;

    private static final Logger log = LoggerFactory.getLogger(ControlService.class);

    public ControlDTO createControl(Integer equipmentTemplateId, Integer signalId, ForeignControlConfigChange foreignSignalConfigChange){
        ControlConfigItem controlConfigItem = null;
        if(doorTemplateProvider.existControl(foreignSignalConfigChange.getForeignControlId())){
            DoorTemplateControl doorTemplateControl = doorTemplateProvider.getTemplateControl(foreignSignalConfigChange.getForeignControlId());
            controlConfigItem = foreignSignalConfigChange.toControlConfigItem(equipmentTemplateId,signalId,doorTemplateControl);
        }else {
            controlConfigItem = foreignSignalConfigChange.toControlConfigItem(equipmentTemplateId,signalId);
        }
        ControlConfigItem result;
        try {
            boolean created = commandProvider.createControl(controlConfigItem);
            result = created ? controlConfigItem : null;
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] CREATE CONTROL: -> error occurs during call config server, the error reason is {}, the call params is {}",ex.getCause(),foreignSignalConfigChange);
            return null;
        }
        ControlDTO controlDTO = new ControlDTO();
        BeanUtils.copyProperties(result, controlDTO);
        return controlDTO;
    }

    public boolean batchDeleteControl(Integer equipmentTemplateId, List<Integer> controlIdList) {
        if(ObjectUtil.isEmpty(equipmentTemplateId) || CollectionUtil.isEmpty(controlIdList)) return false;
        boolean delFlag;
        try {
            delFlag = commandProvider.batchDeleteControl(equipmentTemplateId, controlIdList);
        }catch (Exception ex){
            delFlag = false;
            log.error("[DEVICE LIFE CYCLE MANAGER] BATCH DELETE CONTROL: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the control id list is {}",ex.getCause(),controlIdList);
        }
        return delFlag;
    }

    public Boolean updateControl(Integer equipmentTemplateId, ForeignControl foreignControl, ForeignControlConfigChange controlConfigChange) {
        ControlConfigItem controlConfigItem = controlConfigChange.toControlConfigItem(equipmentTemplateId,null,foreignControl.getControlId(),foreignControl.getControlMeaningsId());
        try {
            return commandProvider.updateControl(controlConfigItem);
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] UPDATE DEVICE CONTROL: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the update control info is {}",ex.getCause(),controlConfigChange);
            return false;
        }
    }

    public ControlDTO getControlInfo(Integer equipmentTemplateId,Integer controlId){
        if(ObjectUtil.isEmpty(equipmentTemplateId) || ObjectUtil.isEmpty(controlId))return null;
        ControlConfigItem controlInfo = null;
        try {
            controlInfo = commandProvider.getControlInfo(equipmentTemplateId, controlId);
        }catch (Exception ex){
            log.error("[DEVICE LIFE CYCLE MANAGER] QUERY CONTROL: -> error occurs during call config server, the error reason is {}, the call equipment template id is {}, and the control id is {}",ex.getCause(),controlId);
            return null;
        }
        ControlDTO controlDTO = new ControlDTO();
        BeanUtils.copyProperties(controlInfo, controlDTO);
        return controlDTO;
    }
}

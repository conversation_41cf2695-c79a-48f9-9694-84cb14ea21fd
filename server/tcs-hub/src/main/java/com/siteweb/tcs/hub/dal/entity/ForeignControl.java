package com.siteweb.tcs.hub.dal.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Objects;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Accessors(chain = true)
@TableName(value = "tcs_foreign_control")
public class ForeignControl  implements Serializable {
    @TableField(exist = false)
    private String foreignGatewayId;
    @TableField(exist = false)
    private String foreignDeviceId;
    @TableField(value = "ForeignControlId")
    private String foreignControlId;
    @TableField(exist = false)
    private int monitorUnitId;
    @TableField(value = "EquipmentId")
    private int equipmentId;
    @TableField(value = "ControlId")
    private int controlId;
    @TableField(value = "ControlMeaningsId")
    private int controlMeaningsId;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ForeignControl that = (ForeignControl) o;
        return equipmentId == that.equipmentId && controlId == that.controlId;
    }

    @Override
    public int hashCode() {
        return Objects.hash(equipmentId, controlId);
    }
}
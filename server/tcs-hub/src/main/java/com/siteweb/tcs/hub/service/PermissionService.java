package com.siteweb.tcs.hub.service;

import com.siteweb.tcs.hub.dal.dto.MenuTreeFullPathDTO;
import com.siteweb.tcs.hub.dal.dto.RegionDTO;
import com.siteweb.tcs.hub.dal.dto.RolePermissionMapDTO;
import com.siteweb.tcs.hub.dal.entity.MenuItem;
import com.siteweb.tcs.hub.dal.entity.Region;

import java.util.List;

public interface PermissionService {

    MenuItem findMenuTree();


    List<MenuItem> findMenuPermissionTreeById(Integer roleId);

    int createMenuPermissionRoleMap(RolePermissionMapDTO rolePermissionMapDTO,Integer permissionType);


    List<Region> findRegionPermissionTreeById(Integer roleId);

    MenuTreeFullPathDTO getMenuTreeByUserId(Integer userId,List<String> startPluginsName);

    List<RegionDTO> getRegionTreeByUserId();

    List<RegionDTO> findItemTreeByUserId(String pluginId);
    List<RegionDTO> getRegionsByUser();
}

package com.siteweb.tcs.hub.domain.letter.enums;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * @ClassName: EquipmentTypeEnum
 * @descriptions: 设备类型枚举
 * @author: xsx
 * @date: 2024/9/7 9:41
 **/
public enum EquipmentTypeEnum {
    NORMAL_EQUIPMENT(-1,"普通设备"),
    DOOR_EQUIPMENT(82, "门禁设备");

    EquipmentTypeEnum(int _value, String _desc) {
        this.value = _value;
    }

    private final int value;

    @JsonValue
    public int getValue() {
        return this.value;
    }

    @JsonCreator
    public static EquipmentTypeEnum fromInt(int i) {
        for (EquipmentTypeEnum status : EquipmentTypeEnum.values()) {
            if (status.value == i) {
                return status;
            }
        }
        throw new IllegalArgumentException("No enum constant with ordinal " + i);
    }
}

package com.siteweb.tcs.north.s6.dal.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
@TableName(value = "batterydischargerecordctcc")
public class BatteryDischargeRecordCTCC {

    @TableId(value = "Id",type = IdType.AUTO)
    private Integer id;

    @TableField(value = "StationId")
    private Integer stationId;

    @TableField(value = "EquipmentId")
    private Integer equipmentId;

    @TableField(value = "StartTime")
    private LocalDateTime startTime;

    @TableField(value = "EndTime")
    private LocalDateTime endTime;
}

package com.siteweb.tcs.north.s6.dal.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.mapping.StatementType;

import java.util.Map;

/**
 * <AUTHOR> (2024-08-15)
 **/
@Mapper
public interface ControlMapper {
    @Select(value = "CALL PBL_SaveControlResult(\n" +
            "        #{stationId}, #{monitorUnitId}, #{equipmentId}, #{controlId}, #{serialNo},\n" +
            "        #{startTime}, #{endTime}, #{userId}, #{baseTypeId},\n" +
            "        #{resultCode}, #{result}, #{controlPhase},\n" +
            "        #{ret, mode=OUT, jdbcType=INTEGER})")
    @Options(statementType = StatementType.CALLABLE)
    Integer saveControlResult(Map<String, Object> params);
}

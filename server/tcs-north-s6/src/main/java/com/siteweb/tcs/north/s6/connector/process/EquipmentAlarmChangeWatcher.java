package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.hub.domain.letter.EquipmentAlarmChange;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.dal.entity.EventResponseItem;
import com.siteweb.tcs.north.s6.dal.provider.EventProvider;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.Props;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

public class EquipmentAlarmChangeWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);

    private EventProvider eventProvider = ConnectorDataHolder.getContext().getBean(EventProvider.class);

    public static Props props() {
        return Props.create(EquipmentAlarmChangeWatcher.class);
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(List.class, this::onAlarmChanges)
                .build();
    }

    private void onAlarmChanges(List<EquipmentAlarmChange> alarmChanges) {
        //todo: 当配置变更时，Siteweb6侦听进行更新内存
        probe.info("[AlarmChange] 收到告警变更: " + alarmChanges.toString());
        System.out.println("xsx");
        if(CollectionUtil.isEmpty(alarmChanges)) return;
        List<EventResponseItem> eventResponseItems = Optional.ofNullable(alarmChanges)
                .orElseGet(ArrayList::new).stream()
                .map(e -> EventResponseItem.fromEquipmentAlarmChange(e))
                .toList();
        probe.info("[AlarmChange] 保存告警变更到数据库: " + eventResponseItems);
        eventProvider.saveEventResponse(eventResponseItems);
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }

}

package com.siteweb.tcs.north.s6.connector.process;

import com.siteweb.tcs.common.runtime.PluginScope;
import com.siteweb.tcs.north.s6.config.InfluxDBConfig;
import com.siteweb.tcs.north.s6.connector.letter.*;
import org.influxdb.InfluxDB;
import org.influxdb.dto.BatchPoints;
import org.influxdb.dto.Point;

import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 历史数据批量处理器
 *
 * <AUTHOR> (2024-08-28)
 **/
public class HistoryDataBatchProcessor {
    private final InfluxDBConfig influxDBConfig;
    private final InfluxDB influxDB;
    private final Long offsetOfZone;
    private static final String HISTORY_DATA_MEASUREMENT = "historydatas";

    private static final String HISTORY_STATISTICS_MEASUREMENT = "historystatistics";

    private static final String HISTORY_BAT_CURVE_MEASUREMENT = "historybatcurve";

    /***
     * 批量写入阈值,1W条一个批次
     */
    private static final int BATCH_SIZE = 10000;

    /***
     * 写入数据批处理
     */
    private BatchPoints lastBatch = null;


    public HistoryDataBatchProcessor() {
        ZonedDateTime zonedDateTime = ZonedDateTime.now();
        this.offsetOfZone = zonedDateTime.getOffset().getTotalSeconds() * 1000L;
        this.influxDB = PluginScope.getBean(InfluxDB.class);
        this.influxDBConfig = PluginScope.getBean(InfluxDBConfig.class);
    }

    /**
     * 数据写入InfluxDB，数据会进行分批处理，不会立即写入数据库
     *
     * @param action 数据列表
     * <AUTHOR> (2024/8/28)
     */
    public void write(StoreInfluxDBlAction action) {
        if (lastBatch == null) {
            lastBatch = BatchPoints.database(influxDBConfig.getDefaultDatabase())
                    .build();
        }
        for (InfluxDBStoreItem data : action.getItems()) {
            long timestamp = data.getRecordTime().toInstant(ZoneOffset.UTC).toEpochMilli() * 1_000_000;
            Point.Builder pointBuilder = Point.measurement(HISTORY_DATA_MEASUREMENT)
                    .time(timestamp, TimeUnit.NANOSECONDS)
                    .tag("StationId", data.getStationId())
                    .tag("DeviceId", data.getDeviceId())
                    .tag("SignalId", data.realTimeKey())
                    .tag("SignalType", data.getSignalType())
                    .addField("PointValue", data.getValue());

            // 检查 BaseTypeId 是否为空，如果不为空则添加标签
            if (data.getBaseTypeId() != null && !data.getBaseTypeId().isEmpty()) {
                pointBuilder.tag("BaseTypeId", data.getBaseTypeId());
            }

            // 构建 Point 对象并添加到 batch 中
            Point point = pointBuilder.build();
            lastBatch.point(point);
            if (lastBatch.getPoints().size() >= BATCH_SIZE) {
                flush();  // 满足批量条件时写入
            }
        }
        flush();  // 强制写入剩余数据
    }


    /**
     * 历史统计数据写入InfluxDB
     *
     * @param action 数据列表
     * @auther Li.qupan.pan (2024/9/25)
     */
    public void write(HistoryStatisticsAction action) {
        if (lastBatch == null) {
            lastBatch = BatchPoints.database(influxDBConfig.getDefaultDatabase())
                    .build();
        }
        List<HistoryStatisticsItem> items = action.getItems();
        for (HistoryStatisticsItem item : items) {
            long periodStart = item.getPeriodStart().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            long periodEnd = item.getPeriodEnd().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            // 构建 InfluxDB 的 Point
            Point.Builder pointBuilder = Point.measurement(HISTORY_STATISTICS_MEASUREMENT)
                    .time(periodStart + offsetOfZone, TimeUnit.MILLISECONDS)
                    .tag("StationId", String.valueOf(item.getStationId()))
                    .tag("DeviceId", item.getEquipmentId())
                    .tag("SignalId", item.getEquipmentId() + "." + item.getSignalId())
                    .addField("PeriodStart", periodStart)
                    .addField("PeriodEnd", periodEnd)
                    // 用于展示的时间存为可读的字符串格式
                    .addField("MaxValue", item.getMaxVal())
                    .addField("MaxValueTime", item.getMaxValTime().atZone(ZoneId.systemDefault()).toInstant().toString())
                    .addField("MinValue", item.getMinVal())
                    .addField("MinValueTime", item.getMinValTime().atZone(ZoneId.systemDefault()).toInstant().toString());

            // 构建 Point 并添加到 batch 中
            Point point = pointBuilder.build();
            lastBatch.point(point);
            // 检查是否达到批量写入的条件
            if (lastBatch.getPoints().size() >= BATCH_SIZE) {
                flush();  // 执行批量写入
            }
        }
        flush();  // 强制写入剩余数据
    }

    /**
     * 电池历史曲线数据写入InfluxDB
     *
     * @param action 数据列表
     * @auther Li.qupan.pan (2024/9/25)
     */
    public void write(HistoryBatCurveAction action) {
        if (lastBatch == null) {
            lastBatch = BatchPoints.database(influxDBConfig.getDefaultDatabase())
                    .build();
        }
        List<HistoryBatCurveItem> items = action.getItems();
        for (HistoryBatCurveItem item : items) {
            // 构建 InfluxDB 的 Point
            long timestamp = item.getRecordTime().toInstant(ZoneOffset.UTC).toEpochMilli() * 1_000_000;
            Point.Builder pointBuilder = Point.measurement(HISTORY_BAT_CURVE_MEASUREMENT)
                    .time(timestamp, TimeUnit.NANOSECONDS)
                    .tag("DeviceId", item.getDeviceId())
                    .tag("StationId", item.getStationId())
                    .addField("VoltageValue", item.getVoltageValue())
                    .addField("CurrentValue", item.getCurrentValue())
                    .addField("VoltageType", item.getVoltageType());

            // 构建 Point 并添加到 batch 中
            Point point = pointBuilder.build();
            lastBatch.point(point);
            // 检查是否达到批量写入的条件
            if (lastBatch.getPoints().size() >= BATCH_SIZE) {
                flush();  // 执行批量写入
            }
        }
        flush();  // 强制写入剩余数据
    }


    /***
     * 强制写库，不管这批数据有多少
     * <AUTHOR> (2024/8/28)
     */
    public void flush() {
        var batch = lastBatch;
        if (batch != null && !batch.getPoints().isEmpty()) {
            lastBatch = BatchPoints.database(influxDBConfig.getDefaultDatabase()).build();
            writeDB(batch);
        }
    }


    private void writeDB(BatchPoints batchPoints) {
        this.influxDB.writeWithRetry(batchPoints);
    }


}

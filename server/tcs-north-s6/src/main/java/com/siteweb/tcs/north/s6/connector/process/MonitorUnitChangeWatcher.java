package com.siteweb.tcs.north.s6.connector.process;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.siteweb.tcs.common.o11y.ActorProbe;
import com.siteweb.tcs.common.system.ClusterContext;
import com.siteweb.tcs.hub.domain.letter.MonitorUnitChange;
import com.siteweb.tcs.hub.domain.letter.enums.EnumGatewayConnectState;
import com.siteweb.tcs.hub.domain.process.EnumDeviceConnectState;
import com.siteweb.tcs.north.s6.connector.ConnectorDataHolder;
import com.siteweb.tcs.north.s6.connector.letter.SetRedisItemAction;
import com.siteweb.tcs.north.s6.dal.entity.MonitorUnitState;
import com.siteweb.tcs.north.s6.dal.provider.EquipmentProvider;
import lombok.extern.slf4j.Slf4j;
import nonapi.io.github.classgraph.json.JSONSerializer;
import org.apache.pekko.actor.AbstractActor;
import org.apache.pekko.actor.ActorRef;
import org.apache.pekko.actor.Props;
import org.apache.pekko.actor.Scheduler;
import org.springframework.data.redis.core.RedisTemplate;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.createProbe;
import static com.siteweb.tcs.common.o11y.ProbeManagerAdapter.removeProbe;

@Slf4j
public class MonitorUnitChangeWatcher extends AbstractActor {

    private final ActorProbe probe = createProbe(this);
    private final ActorRef redisSink;

    private final Map<Integer,MonitorUnitChange> monitorUnitChangeMap = new HashMap<>();

    private final Scheduler scheduler = ClusterContext.getActorSystem().scheduler();

    private final RedisTemplate redisTemplate;

    private EquipmentProvider equipmentProvider = ConnectorDataHolder.getContext().getBean(EquipmentProvider.class);

    public MonitorUnitChangeWatcher(ActorRef redisSink,RedisTemplate redisTemplate) {
        this.redisSink = redisSink;
        this.redisTemplate = redisTemplate;
        scheduler.scheduleAtFixedRate(Duration.ofSeconds(30),Duration.ofSeconds(60),()->reportHeartBeat(),getContext().getDispatcher());
    }

    public static Props props(ActorRef redisSink, RedisTemplate redisTemplate) {
        return Props.create(MonitorUnitChangeWatcher.class, () -> new MonitorUnitChangeWatcher(redisSink,redisTemplate));
    }

    @Override
    public Receive createReceive() {
        return receiveBuilder()
                .match(MonitorUnitChange.class, this::saveMonitorUnitChange)
                .build();
    }

    private void saveMonitorUnitChange(MonitorUnitChange monitorUnitChange) {
        //todo: save monitor unit change to redis
        if(ObjectUtil.isEmpty(monitorUnitChange)) return;
        Integer monitorUnitId = monitorUnitChange.getMonitorUnitId();
        boolean isExist = monitorUnitChangeMap.containsKey(monitorUnitId);
        MonitorUnitChange existChange = monitorUnitChangeMap.get(monitorUnitId);
        switch (EnumGatewayConnectState.fromCode(monitorUnitChange.getConnectState())){
            case OFFLINE:
                if(isExist && ObjectUtil.notEqual(existChange.getConnectState(),monitorUnitChange.getConnectState())){
                    monitorUnitChangeMap.remove(monitorUnitId);
                    log.info("[Monitor Unit Connect State] {} connect state change to {}",monitorUnitChange, EnumDeviceConnectState.OFFLINE.getDescription());
                }
                break;
            case ONLINE:
                if(!isExist || ObjectUtil.notEqual(existChange.getConnectState(),monitorUnitChange.getConnectState())){
                    monitorUnitChange.setChange(true);
                    monitorUnitChangeMap.put(monitorUnitChange.getMonitorUnitId(),monitorUnitChange);
                    log.info("[Monitor Unit Connect State] {} connect state change to {}",monitorUnitChange, EnumDeviceConnectState.ONLINE.getDescription());
                }
                break;
        }
    }

    private void reportHeartBeat() {
        if (CollectionUtil.isEmpty(monitorUnitChangeMap)) return;
        SetRedisItemAction setRedisItemAction = new SetRedisItemAction();
        //不能自己改redis的状态，只能在新增时置为在线
        /**
         * 1.根据map的key去redis找所有的回来
         * 2.将查回来的时间戳修改成对应的
         * 3.更新到redis中
         */
        List<String> queryKeyList = monitorUnitChangeMap.values().stream()
                .map(MonitorUnitChange::getRedisKey)
                .toList();
        LocalDateTime now = LocalDateTime.now();
        List<String> jsonStrList = redisTemplate.opsForValue().multiGet(queryKeyList);
        List<MonitorUnitState> monitorUnitStateList = new ArrayList<>();
        Collection<Integer> needCreate;
        //todo 序列化要思考怎么做
        if (CollectionUtil.isNotEmpty(jsonStrList)) {
            jsonStrList.forEach(e -> {
                MonitorUnitState monitorUnitState = JSONUtil.toBean(e, MonitorUnitState.class);
                monitorUnitStateList.add(monitorUnitState);
            });
            Set<Integer> existIdSet = monitorUnitStateList.stream()
                    .map(MonitorUnitState::getMonitorUnitId).collect(Collectors.toSet());
            Set<Integer> allIdSet = monitorUnitChangeMap.keySet();
            needCreate = CollectionUtil.subtract(allIdSet, existIdSet);
            Set<Integer> needUpdate = CollectionUtil.intersectionDistinct(existIdSet, allIdSet);
            monitorUnitStateList.forEach(e -> {
                if (needUpdate.contains(e.getMonitorUnitId())) {
                    e.setHeartBeatTime(MonitorUnitState.formatDateTime(now));
                    setRedisItemAction.addItem(MonitorUnitChange.getRedisKey(e.getMonitorUnitId()), JSONSerializer.serializeObject(e));
                }
            });
            if (CollectionUtil.isNotEmpty(needCreate)) {
                List<Integer> onlineMuIdList = new ArrayList<>();
                monitorUnitChangeMap.entrySet().stream().filter(e -> needCreate.contains(e.getKey()))
                        .map(Map.Entry::getValue)
                        .forEach(e -> {
                            setRedisItemAction.addItem(e.getRedisKey(), JSONSerializer.serializeObject(MonitorUnitState.fromMonitorUnitChange(e)));
                            onlineMuIdList.add(e.getMonitorUnitId());
                        });
                //更新S6设备表中状态为未启用的改为在线，因为FSU在线
//                if (CollectionUtil.isNotEmpty(onlineMuIdList))
//                    equipmentProvider.updateConnectStateByMonitorUnitId(onlineMuIdList, 2, 1);
            }
        } else {
            List<Integer> onlineMuIdList = new ArrayList<>();
            monitorUnitChangeMap.values().stream()
                    .forEach(e -> {
                        setRedisItemAction.addItem(e.getRedisKey(), JSONSerializer.serializeObject(MonitorUnitState.fromMonitorUnitChange(e)));
                        e.setChange(false);
                        onlineMuIdList.add(e.getMonitorUnitId());
                    });
//            if (CollectionUtil.isNotEmpty(onlineMuIdList))
//                equipmentProvider.updateConnectStateByMonitorUnitId(onlineMuIdList, 2, 1);
        }
        redisSink.tell(setRedisItemAction, getSelf());
        // todo 2024/11/25 xsx 修改设备状态由未启用改为在线，这个按理需要添加在EquipmentLifeCycleEventWatcher，但是目前没有时间测试调数据流，先在mu的心跳中添加，后续修改
        equipmentProvider.updateConnectStateByMonitorUnitId(monitorUnitChangeMap.keySet(),2,1);
    }

    @Override
    public void postStop() {
        removeProbe(this.probe);
    }

}

